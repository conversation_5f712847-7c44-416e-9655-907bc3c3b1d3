# Usar Python 3.6 como imagen base
FROM python:3.6-slim

# Establecer el directorio de trabajo
WORKDIR /app

# Instalar dependencias del sistema
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copiar el archivo de requirements específico para Python 3.6
COPY requirements-py36.txt requirements.txt

# Instalar dependencias de Python
RUN pip install --no-cache-dir -r requirements.txt

# Copiar todos los archivos de la aplicación
COPY . .

# Crear directorio para logs
RUN mkdir -p /app/logs

# Establecer permisos
RUN chmod +x *.py

# Exponer puerto (si fuera necesario para futuras funcionalidades web)
EXPOSE 8000

# Comando por defecto
CMD ["python", "main.py", "--help"]
