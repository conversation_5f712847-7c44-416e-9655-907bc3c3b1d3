2025-07-17 10:43:33 - MFA_AUTO - Detectado: mfa.py - Python 3.11 con dataclasses disponible
2025-07-17 10:43:33 - MFA_AUTO - Ejecutando: /opt/anaconda3/bin/python3.11 /Users/<USER>/Documents/esta/bmc-remedy-organized/mfa.py create 1152213619 Test auto 3001234567 123456789 <EMAIL> 2
2025-07-17 10:45:07 - MFA_AUTO - Script mfa.py terminó con código: 0
2025-07-17 10:49:31 - MFA_AUTO - Detectado: mfa.py - Python 3.11 con dataclasses disponible
2025-07-17 10:49:31 - MFA_AUTO - Ejecutando: /opt/anaconda3/bin/python3.11 /Users/<USER>/Documents/esta/bmc-remedy-organized/mfa.py create 1152213619 Test auto 3001234567 123456789 <EMAIL> 2
