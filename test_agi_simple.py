#!/usr/bin/env python3
"""
Script de prueba simple para el AGI de BMC Remedy
"""

import subprocess
import sys
import os

def test_agi_simple():
    print("=== Prueba Simple del AGI ===")
    
    # Comando a ejecutar
    cmd = [
        'python3.11', 'mfa.py',
        'create',
        '1152213619',           # document (cédula)
        'Test ticket detail',   # ticketDetail
        '3001234567',          # cellphone
        '1234567890123',       # uniqueId
        '<EMAIL>',  # destinatario
        '3'                    # limite (número de casos)
    ]
    
    print(f"Ejecutando: {' '.join(cmd)}")
    
    try:
        # Ejecutar sin entrada stdin (modo terminal)
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=120
        )
        
        print(f"Código de salida: {result.returncode}")
        
        if result.stdout:
            print("=== STDOUT ===")
            print(result.stdout)
        
        if result.stderr:
            print("=== STDERR ===")
            print(result.stderr)
        
        if result.returncode == 0:
            print("✅ Prueba exitosa")
        else:
            print("❌ Prueba falló")
            
    except subprocess.TimeoutExpired:
        print("❌ Timeout - el proceso tardó más de 120 segundos")
    except Exception as e:
        print(f"❌ Error ejecutando prueba: {e}")

if __name__ == "__main__":
    test_agi_simple()
