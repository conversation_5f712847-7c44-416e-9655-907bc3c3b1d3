# Integración BMC Remedy con Asterisk

Este documento explica cómo integrar el sistema de búsqueda de tickets BMC Remedy con Asterisk usando AGI (Asterisk Gateway Interface).

## 🚀 Instalación Rápida

1. **Ejecutar el script de instalación:**
   ```bash
   ./install_asterisk.sh
   ```

2. **Verificar la instalación:**
   ```bash
   python3.11 test_agi_simple.py
   ```

## 📋 Requisitos

- **Python 3.7+** (recomendado) o **Python 2.7+** (compatible)
- Asterisk instalado y configurado
- Dependencias del proyecto BMC Remedy instaladas
- Permisos de escritura en el directorio AGI de Asterisk

## 🔧 Versiones Disponibles

### `mfa.py` - Versión Principal (Python 3.7+)
- Requiere Python 3.7 o superior
- Usa dataclasses y características modernas de Python
- Logging avanzado y mejor manejo de errores

### `mfa_simple.py` - Versión Compatible (Python 2.7+)
- Compatible con Python 2.7 y versiones anteriores
- Sin dependencias de dataclasses
- Funcionalidad completa con compatibilidad extendida

## 🔧 Configuración Manual

### 1. Copiar el script AGI

```bash
# Encontrar el directorio AGI de Asterisk
sudo find /var /usr /opt -name "agi-bin" -type d 2>/dev/null

# Crear enlaces simbólicos (reemplazar /path/to/agi-bin con la ruta encontrada)
sudo ln -s /ruta/completa/al/proyecto/mfa.py /path/to/agi-bin/mfa.py
sudo ln -s /ruta/completa/al/proyecto/mfa_simple.py /path/to/agi-bin/mfa_simple.py

# Verificar permisos
sudo chown asterisk:asterisk /path/to/agi-bin/mfa.py
sudo chown asterisk:asterisk /path/to/agi-bin/mfa_simple.py
```

### 2. Configurar Asterisk (extensions.conf)

```ini
[bmc-remedy-context]
; Ejemplo básico
exten => _X.,1,NoOp(=== BMC Remedy Lookup ===)
same => n,Set(DOCUMENT=${ARG1})  ; Cédula del usuario
same => n,Set(DESCRIPCION="Consulta IVR")  ; Descripción de la consulta
same => n,Set(CELLPHONE=${CALLERID(num)})
same => n,Set(UNIQUE_ID=${STRREPLACE(UNIQUEID,".","")})
same => n,AGI(mfa.py,"search","${DOCUMENT}","${DESCRIPCION}","${CELLPHONE}","${UNIQUE_ID}","<EMAIL>","5")
same => n,NoOp(Resultado: ${TICKET_SEARCH_RESULT})
same => n,NoOp(Mensaje: ${TICKET_SEARCH_MESSAGE})
same => n,GotoIf($["${TICKET_SEARCH_RESULT}" = "SUCCESS"]?success:failed)
same => n(success),Playback(custom/reporte-enviado)
same => n,Hangup()
same => n(failed),Playback(custom/error-reporte)
same => n,Hangup()
```

### 3. Ejemplo Avanzado con Validación

```ini
[bmc-remedy-advanced]
exten => _X.,1,NoOp(=== BMC Remedy Lookup ===)
same => n,Set(DOCUMENT=${ARG1})
same => n,GotoIf($[${LEN(${DOCUMENT})} < 8]?invalid:valid)
same => n(invalid),Playback(custom/cedula-invalida)
same => n,Hangup()
same => n(valid),Set(DESCRIPCION="Consulta IVR - ${STRFTIME(${EPOCH},,%Y-%m-%d %H:%M:%S)}")
same => n,Set(CELLPHONE=${CALLERID(num)})
same => n,Set(UNIQUE_ID=${STRREPLACE(UNIQUEID,".","")})
same => n,Playback(custom/buscando-tickets)
same => n,AGI(mfa.py,"search","${DOCUMENT}","${DESCRIPCION}","${CELLPHONE}","${UNIQUE_ID}","<EMAIL>","10")
same => n,NoOp(=== Resultado AGI ===)
same => n,NoOp(TICKET_SEARCH_RESULT: ${TICKET_SEARCH_RESULT})
same => n,NoOp(TICKET_SEARCH_MESSAGE: ${TICKET_SEARCH_MESSAGE})
same => n,GotoIf($["${TICKET_SEARCH_RESULT}" = "SUCCESS"]?success:error)
same => n(success),Playback(custom/reporte-enviado-exitosamente)
same => n,SayDigits(${DOCUMENT})
same => n,Hangup()
same => n(error),Playback(custom/error-al-generar-reporte)
same => n,Verbose(1,Error: ${TICKET_SEARCH_MESSAGE})
same => n,Hangup()

; Ejemplo con límite dinámico basado en el contexto
[bmc-remedy-dynamic]
exten => _X.,1,NoOp(=== BMC Remedy con Límite Dinámico ===)
same => n,Set(DOCUMENT=${ARG1})
same => n,Set(TICKET_DETAIL="Consulta IVR - ${STRFTIME(${EPOCH},,%Y-%m-%d %H:%M:%S)}")
same => n,Set(CELLPHONE=${CALLERID(num)})
same => n,Set(UNIQUE_ID=${STRREPLACE(UNIQUEID,".","")})
; Configurar límite basado en la hora (más casos en horas pico)
same => n,Set(HOUR=${STRFTIME(${EPOCH},,%H)})
same => n,GotoIf($[${HOUR} >= 8 && ${HOUR} <= 17]?business:after)
same => n(business),Set(LIMITE=10)  ; Horario comercial: más casos
same => n,Goto(search)
same => n(after),Set(LIMITE=3)      ; Fuera de horario: menos casos
same => n(search),AGI(mfa.py,"create","${DOCUMENT}","${TICKET_DETAIL}","${CELLPHONE}","${UNIQUE_ID}","<EMAIL>","${LIMITE}")
same => n,NoOp(Límite usado: ${LIMITE})
same => n,GotoIf($["${TICKET_SEARCH_RESULT}" = "SUCCESS"]?success:error)
same => n(success),Playback(custom/reporte-enviado)
same => n,Hangup()
same => n(error),Playback(custom/error-reporte)
same => n,Hangup()
```

## 📞 Uso del Script AGI

### Sintaxis

```
AGI(mfa.py, "create", "${document}", "${ticketDetail}", "${cellphone}", "${uniqueId}", "${destinatario}", "${limite}")
```

### Parámetros

| Parámetro | Descripción | Ejemplo | Requerido |
|-----------|-------------|---------|-----------|
| `action` | Acción a realizar | `"create"` | ✅ |
| `document` | Número de cédula | `"1152213619"` | ✅ |
| `ticketDetail` | Detalle del ticket | `"Consulta telefónica"` | ✅ |
| `cellphone` | Número de teléfono | `"3001234567"` | ✅ |
| `uniqueId` | ID único de llamada | `"${STRREPLACE(UNIQUEID,\".\",\"\")}"` | ✅ |
| `destinatario` | Email destinatario | `"<EMAIL>"` | ❌ |
| `limite` | Número máximo de casos | `"5"` | ❌ |

### Variables de Retorno

El script establece las siguientes variables en Asterisk:

- **`TICKET_SEARCH_RESULT`**: Estado del resultado
  - `"SUCCESS"`: Búsqueda exitosa y reporte enviado
  - `"FAILED"`: Error en la búsqueda o envío
  - `"ERROR"`: Error en los parámetros o ejecución

- **`TICKET_SEARCH_MESSAGE`**: Mensaje descriptivo del resultado

## 🧪 Pruebas

### Prueba Simple
```bash
python3.11 test_agi_simple.py
```

### Prueba Manual
```bash
# Con límite por defecto (5 casos)
python3.11 mfa.py create 1152213619 "Test ticket" "3001234567" "123456789" "<EMAIL>"

# Con límite específico (10 casos)
python3.11 mfa.py create 1152213619 "Test ticket" "3001234567" "123456789" "<EMAIL>" "10"

# Solo 3 casos
python3.11 mfa.py create 1152213619 "Test ticket" "3001234567" "123456789" "<EMAIL>" "3"
```

### Prueba desde Asterisk CLI
```
*CLI> originate Local/1152213619@bmc-remedy-context extension 1152213619@bmc-remedy-context
```

## 📝 Logs

Los logs del AGI se guardan en:
- **Archivo**: `logs/agi.log`
- **Nivel**: INFO y superior
- **Formato**: `YYYY-MM-DD HH:MM:SS - AGI - LEVEL - MESSAGE`

### Ver logs en tiempo real:
```bash
tail -f logs/agi.log
```

## ⚠️ Solución de Problemas

### Error: "No module named 'dataclasses'"
- **Solución**: Use `mfa_simple.py` en lugar de `mfa.py`
- Este error ocurre en Python < 3.7
- La versión simple es totalmente compatible

### Error: "No such file or directory"
- Verificar que el enlace simbólico esté correcto
- Verificar permisos del archivo
- Verificar que python esté instalado y accesible

### Error: "Permission denied"
```bash
sudo chown asterisk:asterisk /path/to/agi-bin/mfa.py
sudo chown asterisk:asterisk /path/to/agi-bin/mfa_simple.py
sudo chmod +x /path/to/agi-bin/mfa.py
sudo chmod +x /path/to/agi-bin/mfa_simple.py
```

### Error: "Module not found"
- Verificar que las dependencias estén instaladas
- Verificar que el directorio del proyecto esté accesible
- Verificar variables de entorno

### Timeout en la ejecución
- Verificar conectividad a BMC Remedy
- Verificar configuración de credenciales
- Revisar logs para errores específicos

## 🔒 Seguridad

- El script se ejecuta con permisos del usuario `asterisk`
- Las credenciales se leen desde variables de entorno
- Los logs pueden contener información sensible
- Configurar rotación de logs apropiada

## 📈 Monitoreo

### Métricas recomendadas:
- Tiempo de respuesta del AGI
- Tasa de éxito/fallo
- Volumen de consultas por hora
- Errores de conectividad

### Alertas recomendadas:
- Fallos consecutivos > 5
- Tiempo de respuesta > 60 segundos
- Errores de autenticación
- Espacio en disco para logs

## 🔄 Mantenimiento

### Rotación de logs:
```bash
# Agregar a logrotate
/path/to/project/logs/agi.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 asterisk asterisk
}
```

### Actualización del script:
1. Detener Asterisk temporalmente si es necesario
2. Actualizar el código del proyecto
3. Probar con `test_agi_simple.py`
4. Reiniciar Asterisk si fue detenido

---

## 📞 Soporte

Para soporte técnico o reportar problemas, revisar:
1. Logs del AGI: `logs/agi.log`
2. Logs de Asterisk: `/var/log/asterisk/`
3. Logs del sistema BMC Remedy

**Ejemplos de llamadas exitosas:**

**Versión principal (Python 3.7+):**
```
same => n,AGI(mfa.py,"create","1152213619","Consulta telefónica","3001234567","1234567890","<EMAIL>","5")
```

**Versión compatible (Python 2.7+):**
```
same => n,AGI(mfa_simple.py,"create","1152213619","Consulta telefónica","3001234567","1234567890","<EMAIL>","5")
```

**Resultado esperado:**
- `TICKET_SEARCH_RESULT = "SUCCESS"`
- `TICKET_SEARCH_MESSAGE = "Reporte generado exitosamente para 1152213619"`
- Email <NAME_EMAIL> con el reporte de tickets

**Recomendación:** Use `mfa_simple.py` si experimenta problemas con dataclasses o tiene Python < 3.7
