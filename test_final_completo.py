#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de prueba final para verificar que tanto work orders como incidentes 
llegan correctamente al correo electrónico
"""

import subprocess
import sys
import time

def test_completo():
    """Prueba completa del sistema optimizado"""
    
    print("=" * 70)
    print("🧪 PRUEBA FINAL COMPLETA - BMC REMEDY OPTIMIZADO")
    print("=" * 70)
    print()
    
    print("📋 CONFIGURACIÓN DE PRUEBA:")
    print("- Cédula: 1152213619 (<PERSON>)")
    print("- Email: <EMAIL>")
    print("- Límite: 3 registros")
    print("- Docker: Python 3.6")
    print()
    
    try:
        print("🚀 Ejecutando consulta optimizada...")
        start_time = time.time()
        
        cmd = [
            "sudo", "docker", "run", "--rm",
            "-v", f"{subprocess.check_output(['pwd'], text=True).strip()}/logs:/app/logs",
            "bmc-remedy-py36-fixed-final",
            "python", "main.py", 
            "1152213619",
            "--destinatario", "<EMAIL>",
            "--limite", "3"
        ]
        
        print("Comando ejecutado:")
        print(" ".join(cmd))
        print()
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print("=" * 70)
        print("📊 RESULTADOS DE LA PRUEBA")
        print("=" * 70)
        
        print(f"⏱️  Tiempo de ejecución: {execution_time:.1f} segundos")
        print(f"🔄 Código de retorno: {result.returncode}")
        print()
        
        if result.stdout:
            print("📤 SALIDA ESTÁNDAR:")
            print("-" * 50)
            print(result.stdout)
            print()
        
        if result.stderr:
            print("⚠️  ERRORES:")
            print("-" * 50)
            print(result.stderr)
            print()
        
        # Analizar resultados
        output_text = result.stdout + result.stderr
        
        # Buscar indicadores de éxito
        tickets_encontrados = "work orders" in output_text.lower()
        incidentes_encontrados = "incidentes" in output_text.lower()
        correo_enviado = "enviado correctamente" in output_text.lower()
        
        print("=" * 70)
        print("✅ VERIFICACIÓN DE FUNCIONALIDADES")
        print("=" * 70)
        
        print(f"🎫 Work Orders encontrados: {'✅ SÍ' if tickets_encontrados else '❌ NO'}")
        print(f"🎯 Incidentes encontrados: {'✅ SÍ' if incidentes_encontrados else '❌ NO'}")
        print(f"📧 Correo enviado: {'✅ SÍ' if correo_enviado else '❌ NO'}")
        print(f"⚡ Tiempo aceptable (<30s): {'✅ SÍ' if execution_time < 30 else '❌ NO'}")
        print(f"🔧 Ejecución exitosa: {'✅ SÍ' if result.returncode == 0 else '❌ NO'}")
        
        print()
        print("=" * 70)
        print("📋 RESUMEN FINAL")
        print("=" * 70)
        
        if result.returncode == 0 and correo_enviado and execution_time < 30:
            print("🎉 ¡ÉXITO TOTAL!")
            print("✅ La aplicación está funcionando perfectamente")
            print("✅ Tanto work orders como incidentes se procesan correctamente")
            print("✅ El correo se envía exitosamente")
            print("✅ El tiempo de respuesta es óptimo para AGI")
            print()
            print("🚀 LISTO PARA PRODUCCIÓN CON ASTERISK AGI")
            return True
        else:
            print("⚠️  PROBLEMAS DETECTADOS:")
            if result.returncode != 0:
                print("❌ Error en la ejecución")
            if not correo_enviado:
                print("❌ El correo no se envió correctamente")
            if execution_time >= 30:
                print("❌ Tiempo de respuesta demasiado lento para AGI")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ TIMEOUT: La consulta tardó más de 2 minutos")
        return False
    except Exception as e:
        print(f"❌ EXCEPCIÓN: {str(e)}")
        return False

def test_performance():
    """Prueba de rendimiento con múltiples ejecuciones"""
    
    print("\n" + "=" * 70)
    print("⚡ PRUEBA DE RENDIMIENTO")
    print("=" * 70)
    
    tiempos = []
    exitos = 0
    
    for i in range(3):
        print(f"\n🔄 Ejecución {i+1}/3...")
        
        try:
            start_time = time.time()
            
            cmd = [
                "sudo", "docker", "run", "--rm",
                "-v", f"{subprocess.check_output(['pwd'], text=True).strip()}/logs:/app/logs",
                "bmc-remedy-py36-fixed-final",
                "python", "main.py", 
                "1152213619",
                "--destinatario", "<EMAIL>",
                "--limite", "3"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            tiempos.append(execution_time)
            
            if result.returncode == 0 and "enviado correctamente" in (result.stdout + result.stderr):
                exitos += 1
                print(f"   ✅ Éxito en {execution_time:.1f}s")
            else:
                print(f"   ❌ Fallo en {execution_time:.1f}s")
                
        except subprocess.TimeoutExpired:
            print(f"   ❌ Timeout en ejecución {i+1}")
        except Exception as e:
            print(f"   ❌ Error en ejecución {i+1}: {str(e)}")
    
    if tiempos:
        tiempo_promedio = sum(tiempos) / len(tiempos)
        tiempo_min = min(tiempos)
        tiempo_max = max(tiempos)
        
        print(f"\n📊 ESTADÍSTICAS DE RENDIMIENTO:")
        print(f"   Éxitos: {exitos}/3 ({exitos/3*100:.1f}%)")
        print(f"   Tiempo promedio: {tiempo_promedio:.1f}s")
        print(f"   Tiempo mínimo: {tiempo_min:.1f}s")
        print(f"   Tiempo máximo: {tiempo_max:.1f}s")
        
        if tiempo_promedio < 20 and exitos >= 2:
            print("   🎯 RENDIMIENTO EXCELENTE PARA AGI")
        elif tiempo_promedio < 30 and exitos >= 2:
            print("   ✅ RENDIMIENTO BUENO PARA AGI")
        else:
            print("   ⚠️  RENDIMIENTO MEJORABLE")

if __name__ == "__main__":
    print("BMC REMEDY - SUITE DE PRUEBAS FINAL")
    print("Verificando funcionalidad completa y rendimiento")
    print()
    
    # Prueba principal
    exito_principal = test_completo()
    
    # Prueba de rendimiento solo si la principal fue exitosa
    if exito_principal:
        test_performance()
    
    print("\n" + "=" * 70)
    print("🏁 PRUEBAS COMPLETADAS")
    print("=" * 70)
    
    if exito_principal:
        print("🎉 Sistema listo para producción!")
        sys.exit(0)
    else:
        print("⚠️  Se requieren ajustes antes de producción")
        sys.exit(1)
