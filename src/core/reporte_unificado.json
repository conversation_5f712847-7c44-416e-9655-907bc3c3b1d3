{"user_info": {"cedula": "1152213619", "found": true, "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "full_name": "<PERSON>", "email": "<EMAIL>", "company": "SURA", "organization": "SEGUROS GENERALES SURAMERICANA S A"}, "tickets": {"cedula": "1152213619", "fecha_consulta": "2025-07-17 01:22:57", "total_tickets": 0, "tickets_procesados": 0, "tickets": []}, "incidents": {"login_id": "1152213619", "fecha_consulta": "2025-07-17 01:22:57", "total_incidentes": 201, "incidentes_procesados": 201, "incidentes": [{"incident_id": "INC000000000100", "status": "Cancelled", "description": "Soporte Especializado Canal Asesor", "detailed_description": "Aplicativo Afectado DWP: Otras\nAplicativo o equipo afectado: Prueba PDN\nRuta por la que ingresa: Prueba PDN\nTelefonos de afectado: 12345\nUbicación del afectado  (Sede y piso): Prueba PDN\nNombre completo del afectado: <PERSON>\nUsuario con el que ingresó: Maripoda\nDescripción del error: Prueba PDN\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: USUARIOS TERCEROS\\nClosure Product Category Tier1: Billing Center\\nResolution Category Tier 2: DEPURAR\\nClosure Product Category Tier2: Movilidad\\nClosure Product Category Tier3: Autos\\nDWP_SRID: 3\\nDWP_SRInstanceID: AGGFPS2SBHR5CAS7R6VKS7R6VKBXGY\\nIncident Number: INC000000000100\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Somos Sura\\nCategorization Tier 3: No puede ingresar\\nProduct Categorization Tier 1: Aplicaciones Transversales\\nProduct Categorization Tier 3: Somos Sura\\nProduct Categorization Tier 2: Somos Sura\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;'maripoda';", "last_modified_date": "2025-02-16T01:08:29.000+0000", "create_date": "2024-08-05T19:59:53.000+0000", "priority": "High", "impact": "2-Significant/Large", "urgency": "2-High", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "AAA", "resolution_method": null, "dwp_number": "3", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Se encuentra validando con proveedor - Prueba.", "last_modified_date": "2024-08-05T21:17:02.000+0000"}}, {"incident_id": "INC000000000120", "status": "Closed", "description": "Soporte Especializado Canal Asesor", "detailed_description": "Aplicativo Afectado DWP: Otras\nAplicativo o equipo afectado: Prueba Consola\nRuta por la que ingresa: Prueba consola\nTelefonos de afectado: 1234567\nUbicación del afectado  (Sede y piso): Viva Envigado\nNombre completo del afectado: <PERSON>\nUsuario con el que ingresó: Maripoda\nDescripción del error: Prueba Consola\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: PROCESO DE NEGOCIO\\nClosure Product Category Tier1: Global Web\\nResolution Category Tier 2: CAPACITACION\\nClosure Product Category Tier2: Produccion\\nClosure Product Category Tier3: Produccion\\nDWP_SRID: 7\\nDWP_SRInstanceID: AGGFPS2SBHR5CAS7SNXHS7SNXHQQIP\\nIncident Number: INC000000000120\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: Cotizador\\nCategorization Tier 3: Bloqueos-Lentitudes\\nProduct Categorization Tier 1: Cotizador Hogar\\nProduct Categorization Tier 3: Hogares Sura\\nProduct Categorization Tier 2: Propiedad\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-04-22T20:49:28.000+0000", "create_date": "2024-08-06T11:30:42.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "7", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000000209", "status": "Cancelled", "description": "Soporte Especializado Canal Asesor", "detailed_description": "Aplicativo Afectado: ContactEmpresarial\nRuta por la que ingresa: Prueba hallazgos\nPóliza: 1\nOperación: 1\nUsuario afectado: Maripoda\nContacto del usuario: 1234567\nDescripción del error: Prueba Hallazgos\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: APLICATIVOS\\nClosure Product Category Tier1: Contact Empresarial\\nResolution Category Tier 2: CAPACITACION\\nClosure Product Category Tier2: Contact Empresarial\\nClosure Product Category Tier3: Contact Empresarial - Contact Manager\\nDWP_SRID: 223\\nDWP_SRInstanceID: AGGBNJMPI2BNYAS80D6VS80D6VD9AF\\nIncident Number: INC000000000209\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: Aplicaciones Canal Asesor\\nCategorization Tier 2: Contact Empresarial\\nCategorization Tier 3: Bloqueos-Lentitudes\\nProduct Categorization Tier 1: Contact Empresarial\\nProduct Categorization Tier 3: Contact Empresarial - Contact Manager\\nProduct Categorization Tier 2: Contact Empresarial\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-04-22T20:49:28.000+0000", "create_date": "2024-08-15T21:25:43.000+0000", "priority": "Low", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Prueba resolución y reapertura del incidente.", "resolution_method": null, "dwp_number": "223", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "SOLUCIÓN DEL CASO: Prueba resolución y reapertura del incidente.", "last_modified_date": "2024-09-25T16:02:48.000+0000"}}, {"incident_id": "INC000000000213", "status": "Cancelled", "description": "Soporte Especializado Canal Asesor", "detailed_description": "Aplicativo Afectado DWP: OtrasEmpresarial\nAplicativo o equipo afectado: Prueba\nRuta por la que ingresa: 1\nTelefonos de afectado: 1012345678\nUbicación del afectado  (Sede y piso): 1\nNombre completo del afectado: q1\nUsuario con el que ingresó: 1\nDescripción del error: 1\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: APLICATIVOS\\nClosure Product Category Tier1: Global Web\\nResolution Category Tier 2: CAPACITACION\\nClosure Product Category Tier2: Produccion\\nClosure Product Category Tier3: Produccion\\nDWP_SRID: 239\\nDWP_SRInstanceID: AGGBNJMPI2BNYAS81WDIS81WDISBI5\\nIncident Number: INC000000000213\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: Cotizador\\nCategorization Tier 3: Bloqueos-Lentitudes\\nProduct Categorization Tier 1: Cotizador\\nProduct Categorization Tier 3: Empresarial\\nProduct Categorization Tier 2: Empresarial\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-04-22T20:49:28.000+0000", "create_date": "2024-08-16T20:43:25.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Revisión campanita", "resolution_method": null, "dwp_number": "239", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Hola, notificación campana portal", "last_modified_date": "2024-09-25T16:02:50.000+0000"}}, {"incident_id": "INC000000000219", "status": "Cancelled", "description": "Soporte Especializado Canal Asesor", "detailed_description": "Aplicativo Afectado DWP: OtrasEmpresarial\nAplicativo o equipo afectado: Prueba\nRuta por la que ingresa: 1\nTelefonos de afectado: 1012345678\nUbicación del afectado  (Sede y piso): 1\nNombre completo del afectado: q1\nUsuario con el que ingresó: 1\nDescripción del error: Prueba Special Handling\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: DUPLICIDAD DE SERVICIO\\nClosure Product Category Tier1: Cotizador\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Empresarial\\nClosure Product Category Tier3: Empresarial\\nDWP_SRID: 328\\nDWP_SRInstanceID: AGGEAEH8A71LBAS8353AS8353A8TEP\\nIncident Number: INC000000000219\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: Cotizador\\nCategorization Tier 3: Bloqueos-Lentitudes\\nProduct Categorization Tier 1: Cotizador\\nProduct Categorization Tier 3: Empresarial\\nProduct Categorization Tier 2: Empresarial\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-04-22T20:49:28.000+0000", "create_date": "2024-08-17T16:34:28.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "328", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000000225", "status": "Cancelled", "description": "Soporte Especializado Canal Asesor", "detailed_description": "Aplicativo Afectado: CotizadorEmpresarial\nTipo de falla: Transacción en proceso\nProducto / Proceso: Nuevo\nProducto: Agentes de Carga\nUsuario afectado: Maripoda\nCelular del afectado: 123456\nRuta por la que ingresa: Noaplica\nCotización: 12345\nPoliza: \nradicado y/o poliza: 12345\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: No\n¿La póliza tiene oneroso?: No\nDescripción del error: Prueba - Cierre Mesa de Ayuda\n¿Cuántas personas hay afectadas?: 1 persona¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: APLICATIVOS\\nClosure Product Category Tier1: Cotizador\\nResolution Category Tier 2: CAPACITACION\\nClosure Product Category Tier2: Empresarial\\nClosure Product Category Tier3: Empresarial\\nDWP_SRID: 249\\nDWP_SRInstanceID: AGGBNJMPI2BNYAS88VZYS88VZY5VJP\\nIncident Number: INC000000000225\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: Cotizador\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: Cotizador\\nProduct Categorization Tier 3: Empresarial\\nProduct Categorization Tier 2: Empresarial\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-04-22T20:49:28.000+0000", "create_date": "2024-08-20T15:22:59.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): prueba\nSolución: \nCausa <PERSON>íz (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO):", "resolution_method": null, "dwp_number": "249", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000000226", "status": "Cancelled", "description": "CotizadorEmpresarial", "detailed_description": "Aplicativo Afectado: CotizadorEmpresarial\nTipo de falla: Transacción en proceso\nProducto / Proceso: Nuevo\nProducto: Agentes de Carga\nUsuario afectado: Maripoda\nCelular del afectado: 123456\nRuta por la que ingresa: Noaplica\nCotización: 12345\nPoliza: \nradicado y/o poliza: 12345\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: Si\n¿La póliza tiene oneroso?: No\nDescripción del error: Prueba - Cierre Nivel 2\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: DATO\\nClosure Product Category Tier1: CORE Policy Center - Empresarial\\nResolution Category Tier 2: MODIFICAR\\nClosure Product Category Tier2: Empresariales\\nClosure Product Category Tier3: Empresariales\\nDWP_SRID: 251\\nDWP_SRInstanceID: AGGBNJMPI2BNYAS88WG3S88WG35X9P\\nIncident Number: INC000000000226\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: Cotizador\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: Cotizador\\nProduct Categorization Tier 3: Empresarial\\nProduct Categorization Tier 2: Empresarial\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-04-22T20:49:28.000+0000", "create_date": "2024-08-20T15:26:55.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Encolamiento BC/SAP - HU-286860 - Errores de encolamiento en el proceso POLICYCLOSURE\n\n* Agrupador del error: #Negocio | \n* Causa del error: 6. Errores de la aplicación | \n* Proceso del error: 2. Renovacion | \n* HU raizal / mejora:  | \n* Estado raizal: Cap | \n* Responsable solución: 1. TI | \n* Diagnostico: prueba | \n* Acción ejecutada:  | \n* Descripción de solución:  | \n* Confirmar operatividad del usuario afectado:  | \n* OC acceso a PDN- (PAM):  |", "resolution_method": null, "dwp_number": "251", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "falta de información del usuario", "last_modified_date": "2024-09-25T16:02:52.000+0000"}}, {"incident_id": "INC000000000229", "status": "Cancelled", "description": "Contact Empresarial", "detailed_description": "Aplicativo Afectado: ContactEmpresarial\nRuta por la que ingresa: Prueba - Contact Center\nPóliza: 1234567\nOperación: 1234\nUsuario afectado: Prueba - Contact Center\nContacto del usuario: 1234567\nDescripción del error: Prueba\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-4D50E3EA539A11EFB3251AF001D8BC3E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: TRASACCION\\nClosure Product Category Tier1: Contact Empresarial\\nResolution Category Tier 2: RELANZAR\\nClosure Product Category Tier2: Contact Empresarial\\nHPD_CI: ContactManager\\nClosure Product Category Tier3: Contact Empresarial - Contact Manager\\nDWP_SRID: 262\\nDWP_SRInstanceID: AGGBNJMPI2BNYAS895K6S895K6LV7U\\nIncident Number: INC000000000229\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: Aplicaciones Canal Asesor\\nCategorization Tier 2: Contact Empresarial\\nCategorization Tier 3: Bloqueos-Lentitudes\\nProduct Categorization Tier 1: Contact Empresarial\\nProduct Categorization Tier 3: Contact Empresarial - Contact Manager\\nProduct Categorization Tier 2: Contact Empresarial\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-04-22T20:49:28.000+0000", "create_date": "2024-08-20T22:24:47.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Proceso del Error: 4. Re<PERSON><PERSON> / cobros |\n* Agrupador del Error: 1. Enviar recibos a SAP |\n* Causa del Error: 6. Errores de la aplicación |\n* HU Raizal / Mejora: 12345 |\n* Estado Raizal: 1. Identificada |\n* Responsable Solucion: 3. TI/Negocio |\n* Diagnostico: Lorem ipsum dolor sit amet, consectetur adipiscing elit. . . Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. . . Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.. . Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatu. |\n* Accion Ejecutada: Relanzar <PERSON>ac<PERSON>. |\n* Descripcion de Solucion: Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. . . Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. . . Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.. . Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.  |\n* Confirmar operatividad del usuario Afectado: No |\n* Causa Raíz (Identificada/Sin identificar): Identificada |\n* OC Acceso a PDN- (PAM): NA |\"", "resolution_method": null, "dwp_number": "262", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000000230", "status": "Cancelled", "description": "Billing Center Empresarial", "detailed_description": "Aplicativo Afectado: BillingCenterEmpresarial\nTipo de falla: Estados inconsistentes\nRuta por la que ingresa: 1\nProducto o solución afectada: 1\nPóliza: 1\nOperación: 1\nUsuario afectado: 1\nContacto del usuario: 1\nNombre de la oficina: 1\nFactura: 1\n¿Es asesor o cliente crítico?: No\n¿La póliza tiene oneroso?: No\nDescripción del error: 1\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-4D13DBD0539A11EFB2E21AF001D8BC3E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: PROCESO DE NEGOCIO\\nClosure Product Category Tier1: Billing Center\\nResolution Category Tier 2: CAPACITACION\\nClosure Product Category Tier2: Empresariales\\nHPD_CI: Billing Center Empresarial\\nClosure Product Category Tier3: Empresariales\\nDWP_SRID: 263\\nDWP_SRInstanceID: AGGBNJMPI2BNYAS895R2S895R2LXTL\\nIncident Number: INC000000000230\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: Billing Center\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: Billing Center\\nProduct Categorization Tier 3: Empresariales\\nProduct Categorization Tier 2: Empresariales\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-04-22T20:49:28.000+0000", "create_date": "2024-08-20T22:28:56.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Hola, la solución brindada fue XYZ.", "resolution_method": null, "dwp_number": "263", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "<PERSON><PERSON>, estoy disponible ya. Por favor comunícate conmigo.", "last_modified_date": "2024-09-25T16:02:54.000+0000"}}, {"incident_id": "INC000000000232", "status": "Cancelled", "description": "Servicio de Falla DWP Canal Asesor", "detailed_description": "Aplicativo Afectado: CotizadorEmpresarial\nTipo de falla: Transacción en proceso\nProducto / Proceso: Nuevo\nProducto: Agentes de Carga\nUsuario afectado: Maripoda\nCelular del afectado: 123456\nRuta por la que ingresa: Noaplica\nCotización: 12345\nPoliza: \nradicado y/o poliza: 12345\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: Si\n¿La póliza tiene oneroso?: No\nDescripción del error: Prueba - Cierre Nivel 1wekfmf\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nClosure Product Category Tier1: Cotizador\\nClosure Product Category Tier2: Empresarial\\nClosure Product Category Tier3: Empresarial\\nDWP_SRID: 266\\nDWP_SRInstanceID: AGGBNJMPI2BNYAS8KRXHS8KRXHV474\\nIncident Number: INC000000000232\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: Cotizador\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: Cotizador\\nProduct Categorization Tier 3: Empresarial\\nProduct Categorization Tier 2: Empresarial\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-04-22T20:49:28.000+0000", "create_date": "2024-08-21T15:48:51.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "266", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "<PERSON><PERSON>, te comparto la solución", "last_modified_date": "2024-09-25T16:02:54.000+0000"}}, {"incident_id": "INC000000000233", "status": "Cancelled", "description": "Cotizador- Empresarial", "detailed_description": "Aplicativo Afectado: CotizadorEmpresarial\nTipo de falla: Controles y Requisitos\nProducto / Proceso: Nuevo\nProducto: Cumplimiento\nUsuario afectado: 2\nCelular del afectado: 22345678\nRuta por la que ingresa: 2\nCotización: 1\nPoliza: \nradicado y/o poliza: 2\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: No\n¿La póliza tiene oneroso?: No\nDescripción del error: 1\n¿Cuántas personas hay afectadas?: 1 persona¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: APLICATIVOS\\nClosure Product Category Tier1: Cotizador\\nResolution Category Tier 2: CAPACITACION\\nClosure Product Category Tier2: Empresarial\\nClosure Product Category Tier3: Empresarial\\nDWP_SRID: 355\\nDWP_SRInstanceID: AGGEAEH8A71LBAS8LCA8S8LCA8BYPS\\nIncident Number: INC000000000233\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: Cotizador\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: Cotizador\\nProduct Categorization Tier 3: Empresarial\\nProduct Categorization Tier 2: Empresarial\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-04-22T20:49:28.000+0000", "create_date": "2024-08-21T19:27:17.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente):  Prueba resolución G5\nSolución: Prueba\nCausa Raíz (Identificada/Sin identificar): Identificada\nConfirmar operatividad del usuario Afectado (SI/NO): No", "resolution_method": null, "dwp_number": "355", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "<PERSON><PERSON>, me puedes llamar ya, estoy disponible.", "last_modified_date": "2024-09-25T16:02:55.000+0000"}}, {"incident_id": "INC000000000234", "status": "Cancelled", "description": "Cotizador- Empresarial", "detailed_description": "Aplicativo Afectado: CotizadorEmpresarial\nTipo de falla: Transacción en proceso\nProducto / Proceso: Nuevo\nProducto: Agentes de Carga\nUsuario afectado: Prueba - Prioridad Baja\nCelular del afectado: 123456\nRuta por la que ingresa: Prueba\nCotización: 1\nPoliza: \nradicado y/o poliza: 12345\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: No\n¿La póliza tiene oneroso?: No\nDescripción del error: PRUEBA - OMITIR\n¿Cuántas personas hay afectadas?: 1 persona¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-4BB47272539A11EFB1721AF001D8BC3E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: RECIBO\\nClosure Product Category Tier1: Aplicaciones Canal Asesor\\nResolution Category Tier 2: CREAR\\nClosure Product Category Tier2: Cotizador\\nHPD_CI: Cotizador Hogar\\nClosure Product Category Tier3: Hogar y PES\\nDWP_SRID: 267\\nDWP_SRInstanceID: AGGBNJMPI2BNYAS8M5WVS8M5WVGW3C\\nIncident Number: INC000000000234\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: Cotizador\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: Cotizador\\nProduct Categorization Tier 3: Empresarial\\nProduct Categorization Tier 2: Empresarial\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-04-22T20:49:28.000+0000", "create_date": "2024-08-22T13:25:28.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "\"* Gestionado Por: <PERSON> |\n* OC Acceso a PDN- (PAM): 388134 |\n* Agrupador del Error: 4. Cancelación Banca |\n* Causa del Error: 6. Errores de la aplicación  |\n* Proceso del Error: 11. Cancelaciones |\n* HU Raizal / Mejora: 66443 |\n* Estado Raizal: 1. Identificada |\n* Responsable Solucion: 1. TI |\n* Diagnostico: Diferido errado luego de una cancelación |\n* Accion Ejecutada: Se ajusta diferido |\n* Descripcion de Solucion: Buenas tardes, el error se da porque el sistema no calculó correctamente el valor del recibo de cancelación, se ajusta diferido.\nMuchas gracias, feliz día. |\n* Confirmar operatividad del usuario Afectado: Si |\n* ID Formulario de Solicitud de Credenciales: N/A |\"", "resolution_method": null, "dwp_number": "267", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "<PERSON><PERSON>, su ayuda no estoy deacuerdo con la suspensión.", "last_modified_date": "2024-09-25T16:02:56.000+0000"}}, {"incident_id": "INC000000000322", "status": "Cancelled", "description": "Soporte Especializado Canal Asesor", "detailed_description": "Aplicativo Afectado: CotizadorEmpresarial\nTipo de falla: Transacción en proceso\nProducto / Proceso: Nuevo\nProducto: Agentes de Carga\nUsuario afectado: Maripoda\nCelular del afectado: 12345\nRuta por la que ingresa: Prueba\nCotización: 1234\nPoliza: \nradicado y/o poliza: 12345\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: No\n¿La póliza tiene oneroso?: No\nDescripción del error: Hola prueba\n¿Cuántas personas hay afectadas?: 1 persona¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: MEMORIA\\nClosure Product Category Tier1: Group Center\\nResolution Category Tier 2: AUMENTO\\nClosure Product Category Tier2: Empresariales\\nClosure Product Category Tier3: Mascotas\\nDWP_SRID: 244\\nDWP_SRInstanceID: AGGBNJMPI2BNYAS83JDHS83JDHX00E\\nIncident Number: INC000000000322\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: Cotizador\\nCategorization Tier 3: Bloqueos-Lentitudes\\nProduct Categorization Tier 1: Cotizador\\nProduct Categorization Tier 3: Empresarial\\nProduct Categorization Tier 2: Empresarial\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-04-22T20:49:28.000+0000", "create_date": "2024-08-17T14:20:46.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Prueba encuesta de satisfacción.", "resolution_method": null, "dwp_number": "244", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000000326", "status": "Cancelled", "description": "Soporte Especializado Canal Asesor", "detailed_description": "Aplicativo Afectado: CotizadorEmpresarial\nTipo de falla: Transacción en proceso\nProducto / Proceso: Nuevo\nProducto: Agentes de Carga\nUsuario afectado: 12345\nCelular del afectado: 12345567\nRuta por la que ingresa: 12345\nCotización: Prueba\nPoliza: \nradicado y/o poliza: Prueba\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: No\n¿La póliza tiene oneroso?: No\nDescripción del error: Prueba\n¿Cuántas personas hay afectadas?: 1 persona¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nClosure Product Category Tier1: Cotizador\\nClosure Product Category Tier2: Empresarial\\nClosure Product Category Tier3: Empresarial\\nDWP_SRID: 248\\nDWP_SRInstanceID: AGGBNJMPI2BNYAS833RMS833RMYDGC\\nIncident Number: INC000000000326\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: Cotizador\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: Cotizador\\nProduct Categorization Tier 3: Empresarial\\nProduct Categorization Tier 2: Empresarial\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-04-22T20:49:28.000+0000", "create_date": "2024-08-17T16:01:05.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "248", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000000327", "status": "Cancelled", "description": "Soporte Especializado Canal Asesor", "detailed_description": "Prueba", "technical_info": "Associated Alarm: None\\nClosure Product Category Tier1: Aplicaciones Canal Asesor\\nClosure Product Category Tier2: Cotizador\\nClosure Product Category Tier3: Hogar y PES\\nIncident Number: INC000000000327\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: Cotizador\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: Aplicaciones Canal Asesor\\nProduct Categorization Tier 3: Hogar y PES\\nProduct Categorization Tier 2: Cotizador\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-04-22T20:49:28.000+0000", "create_date": "2024-08-17T16:17:13.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO):", "resolution_method": null, "dwp_number": "INC000000000327", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000000529", "status": "Closed", "description": "Cotizador- Empresarial", "detailed_description": "Aplicativo Afectado: CotizadorEmpresarial\nTipo de falla: Cierre de Transacción\nProducto / Proceso: Nuevo\nProducto: Agentes de Carga\nUsuario afectado: Maripoda\nCelular del afectado: 123456\nRuta por la que ingresa: Prueba Producción - Equipo\nCotización: Prueba Producción - Equipo\nPoliza: \nRadicado y/o poliza: 12345\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: No\n¿La póliza tiene oneroso?: No\nDescripción del error: Prueba Producción - Equipo\n¿Cuántas personas hay afectadas?: 1 persona¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 578\\nDWP_SRInstanceID: AGGEAEH8A71LBAS9JGUUS9JGUU63YH\\nIncident Number: INC000000000529\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC (Pruebas)\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-03T20:14:30.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Estamos probando los hallazgos registrados en el Excel - Tercera resolución.", "resolution_method": null, "dwp_number": "578", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Hola prueba nota pública usuario final.", "last_modified_date": "2024-09-03T20:17:15.000+0000"}}, {"incident_id": "INC000000000531", "status": "Cancelled", "description": "Cotizador - Empresarial", "detailed_description": "Aplicativo Afectado: CotizadorEmpresarial\nTipo de falla: Cierre de Transacción\nProducto / Proceso: Nuevo\nProducto: Agentes de Carga\nUsuario afectado: Maripoda\nCelular del afectado: 123456\nRuta por la que ingresa: Prueba Producción - Equipo Oneroso\nCotización: Prueba Producción - Equipo Oneroso\nPoliza: \nRadicado y/o poliza: 12345\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: No\n¿La póliza tiene oneroso?: Si\nDescripción del error: Prueba Producción - Equipo Oneroso\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 582\\nDWP_SRInstanceID: AGGEAEH8A71LBAS9JI86S9JI866TUH\\nIncident Number: INC000000000531\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC (Pruebas)\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-03T20:50:12.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "582", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000000552", "status": "Cancelled", "description": "Billing Center Empresarial", "detailed_description": "Aplicativo Afectado: BillingCenterEmpresarial\nTipo de falla: Controles y Requisitos\nRuta por la que ingresa: Http://Prueba.com\nProducto o solución afectada: Movilidad\nPóliza: 12345\nOperación: Expedición\nUsuario afectado: Maripoda\nContacto del usuario: 123456\nNombre de la oficina: Viva Envigado\nFactura: 123456\n¿La póliza tiene oneroso?: No\nDescripción del error: OMITIR PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 635\\nDWP_SRInstanceID: AGGBNJMPI2BNYAS92LCES92LCEOMOK\\nIncident Number: INC000000000552\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC (Pruebas)\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-05T16:15:21.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "PRUEBA CIERRE - SOLUCIÓN", "resolution_method": null, "dwp_number": "635", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "<PERSON><PERSON>, su ayuda porfavor ractivando el caso porque no estoy de acuerdo con la suspensión.", "last_modified_date": "2024-09-05T22:30:15.000+0000"}}, {"incident_id": "INC000000000621", "status": "Closed", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: OtrasEmpresarial\nAplicativo o equipo afectado: Pruebas PDN\nRuta por la que ingresa: Pruebas PDN\nTelefonos de afectado: 123456\nUbicación del afectado  (Sede y piso): Pruebas PDN\nNombre completo del afectado: <PERSON>\nUsuario con el que ingresó: Maripoda\nDescripción del error: Pruebas PDN - 3 sep\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: APLICATIVOS\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CAPACITACION\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 481\\nDWP_SRInstanceID: AGGBNJMPI2BNYAS9I7PUS9I7PUUQIK\\nIncident Number: INC000000000621\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC (Pruebas)\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-03T11:11:42.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Prueba PDN - Segundo cierre luego de reapertura.", "resolution_method": null, "dwp_number": "481", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Prueba comentario privado - No debe llegar correo electrónico.", "last_modified_date": "2024-09-03T16:14:47.000+0000"}}, {"incident_id": "INC000000000645", "status": "Cancelled", "description": "Group Center Empresarial", "detailed_description": "Aplicativo Afectado: GroupCenterEmpresarial\nTipo de falla: Controles y Requisitos\nRuta por la que ingresa: PRUEBA FORMACIÓN NEGOCIO\nProducto o solución afectada: Movilidad\nPóliza: 122345\nOperación: Expedición\nUsuario afectado: Maripoda\nContacto del usuario: 1234567\nNombre de la oficina: Viva Envigado\nCotización: 1223456\n¿La póliza tiene oneroso?: No\nDescripción del error: OMITIR ES UNA PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 625\\nDWP_SRInstanceID: AGGBNJMPI2BNYAS924EVS924EVMVG1\\nIncident Number: INC000000000645\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC (Pruebas)\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-05T13:53:45.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Hola, resolución del caso de prueba.", "resolution_method": null, "dwp_number": "625", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "<PERSON>uen día, su ayuda por favor reactivando el caso porque no estaba disponible, gracias.", "last_modified_date": "2024-09-05T14:09:49.000+0000"}}, {"incident_id": "INC000000000665", "status": "Cancelled", "description": "<PERSON><PERSON><PERSON>", "detailed_description": "Aplicativo Afectado: Vivemas\nProducto / Proceso: Vive Mas - Salud Financiera\nDocumento del cliente afectado: 12345\nDescripción del error: PRUEBA - OMITIR\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-4D3A945A539A11EFB30D1AF001D8BC3E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: DUPLICIDAD DE SERVICIO\\nClosure Product Category Tier1: Cotizador\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Empresarial\\nHPD_CI: Vive Mas MVP\\nClosure Product Category Tier3: Empresarial\\nDWP_SRID: 667\\nDWP_SRInstanceID: AGGBNJMPI2BNYAS99SA1S99SA1YTNB\\nIncident Number: INC000000000665\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: Cotizador\\nCategorization Tier 3: Bloqueos-Lentitudes\\nProduct Categorization Tier 1: Cotizador\\nProduct Categorization Tier 3: Empresarial\\nProduct Categorization Tier 2: Empresarial\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:09:31.000+0000", "create_date": "2024-09-09T13:27:46.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "667", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "<PERSON><PERSON>, prueba nota publica.", "last_modified_date": "2024-09-09T15:29:00.000+0000"}}, {"incident_id": "INC000000000734", "status": "Cancelled", "description": "Facturación Electrónica", "detailed_description": "Aplicativo Afectado: FacturacionElectrónica\nNombre completo: Maripoda\nCelular: 12345678\nCódigo ramo: \nRecibo: \nPóliza: 12345\nNIT Cliente: M-1234\nDescripción del error: PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: Si\nMedio de Ingreso: Entes de control\n¿Cuál es el numero de la PQRS?: 1234", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 838\\nDWP_SRInstanceID: AGGBNJMPI2BNYAS9NSCJS9NSCJVMA2\\nIncident Number: INC000000000734\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-11T17:20:43.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "838", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000000735", "status": "Cancelled", "description": "Facturación Electrónica", "detailed_description": "Aplicativo Afectado: FacturacionElectrónica\nNombre completo: Maripoda\nCelular: 12345\nCódigo ramo: 1A\nRecibo: 12345\nPóliza: 12345\nNIT Cliente: M-1234\nDescripción del error: PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 841\\nDWP_SRInstanceID: AGGBNJMPI2BNYAS9NSKDS9NSKDVOLM\\nIncident Number: INC000000000735\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-11T17:29:36.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "841", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000000736", "status": "Cancelled", "description": "Cotizador -  Movilidad", "detailed_description": "Aplicativo Afectado: CotizadorMovilidad\nProducto/ Proceso: Nuevo\nCotización: 12345\nUsuario afectado: Maripoda\nRuta por la que ingresa: 12345\nRadicado y/o poliza: \n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: Si\nDescripción del error : <PERSON><PERSON><PERSON>\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 842\\nDWP_SRInstanceID: AGGBNJMPI2BNYAS9NSQDS9NSQDVS51\\nIncident Number: INC000000000736\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-11T17:34:48.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "842", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000000737", "status": "Cancelled", "description": "Cotizador- Vida y Renta", "detailed_description": "Aplicativo Afectado: CotizadorVidaRenta\nProducto/ Proceso: Modificación\nProducto: Accidentes personales\nUsuario afectado: Maripoda\nCelular del afectado: 12345\nRuta por la que ingresa: 12345\nCotización: \nRadicado y/o poliza: \nLote: \n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: Si\n¿Su inconveniente es para la expedición de una póliza con beneficiario oneroso?: No\nDescripción del error: PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 843\\nDWP_SRInstanceID: AGGBNJMPI2BNYAS9NSXUS9NSXUVUC1\\nIncident Number: INC000000000737\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-11T17:43:40.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "843", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000000738", "status": "Cancelled", "description": "Cotizador -  Movilidad", "detailed_description": "Aplicativo Afectado: CotizadorMovilidad\nProceso: Nuevo\nCotización: 1234\nUsuario afectado: Maripoda\nRuta por la que ingresa: PRUEBA\nRadicado y/o poliza: 1234\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: Si\nDescripción del error : PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 844\\nDWP_SRInstanceID: AGGBNJMPI2BNYAS9NT3IS9NT3IVWML\\nIncident Number: INC000000000738\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-11T17:49:09.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "844", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000000874", "status": "Cancelled", "description": "Asistente Virtual Asesor-AVA", "detailed_description": "Aplicativo Afectado: AsistenteVirtualAsesor_AVA\nUsuario Autenticación (DNI): PRUEBA\nCódigo de asesor: 123456\nRuta del menú por la que ingresa: PRUEBA\nPóliza: PRUEBA\nRamo: PRUEBA\nParámetros filtro de búsqueda: PRUEBA\nTeléfonos del afectado (Celular, Fijo, Ext): 123456\nDescripción del error: PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿Su inconveniente es con el ingreso AVA?: No\n¿El reporte surge de una PQRS de Salesforce?: Si\nMedio de Ingreso: Derechos de petición\n¿Cuál es el numero de la PQRS?: 123456", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 873\\nDWP_SRInstanceID: AGGBNJMPI2BNYAS9PVL0S9PVL00XCK\\nIncident Number: INC000000000874\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-12T20:30:35.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "873", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000000875", "status": "Cancelled", "description": "Asistente Virtual Asesor-AVA", "detailed_description": "Aplicativo Afectado: AsistenteVirtualAsesor_AVA\nUsuario Autenticación (DNI): PRUEBA\nCódigo de asesor: 123456\nRuta del menú por la que ingresa: PRUEBA\nPóliza: PRUEBA\nRamo: PRUEBA\nParámetros filtro de búsqueda: PRUEBA\nTeléfonos del afectado (Celular, Fijo, Ext): 123456\nDescripción del error: PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿Su inconveniente es con el ingreso AVA?: No\n¿El reporte surge de una PQRS de Salesforce?: Si\nMedio de Ingreso: Derechos de petición\n¿Cuál es el numero de la PQRS?: 123456", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 997\\nDWP_SRInstanceID: AGGEAEH8A71LBAS9PWGMS9PWGMZUL6\\nIncident Number: INC000000000875\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-12T20:42:37.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "997", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000000877", "status": "Cancelled", "description": "Asistente Virtual Asesor-AVA", "detailed_description": "Aplicativo Afectado: AsistenteVirtualAsesor_AVA\nUsuario Autenticación (DNI): PRUEBA\nCódigo de asesor: 123456\nRuta del menú por la que ingresa: PRUEBA\nPóliza: PRUEBA\nRamo: PRUEBA\nParámetros filtro de búsqueda: PRUEBA\nTeléfonos del afectado (Celular, Fijo, Ext): 123456\nDescripción del error: PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿Su inconveniente es con el ingreso AVA?: No\n¿El reporte surge de una PQRS de Salesforce?: Si\nMedio de Ingreso: Derechos de petición\n¿Cuál es el numero de la PQRS?: 123456", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 998\\nDWP_SRInstanceID: AGGEAEH8A71LBAS9PW8LS9PW8LZXG1\\nIncident Number: INC000000000877\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-12T20:49:43.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "998", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000001047", "status": "Cancelled", "description": "Billing Center Movilidad", "detailed_description": "Aplicativo Afectado: BillingCenterMovilidad\nRuta por la que ingresa: 12345\nPóliza: 12345\nOperación: PRUEBA\nUsuario afectado: Maripoda\nContacto del usuario: 12345\nNombre de la oficina: PRUEBA\nFactura: 12345\nDescripción del error: PRUEBA\n¿La póliza tiene oneroso?: No\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 1054\\nDWP_SRInstanceID: AGGEAEH8A71LBAS9RIQ2S9RIQ2I8DW\\nIncident Number: INC000000001047\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-13T14:11:52.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "1054", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000001062", "status": "Cancelled", "description": "SOAT", "detailed_description": "Aplicativo Afectado: SOAT\nRuta por la que ingresa: PRUEBA\nPóliza: 12345\nOperación: 12345\nUsuario afectado: PRUEBA\nContacto del usuario: 123456\nDescripción del error: PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 1168\\nDWP_SRInstanceID: AGGBNJMPI2BNYAS9RLAIS9RLAIOMCA\\nIncident Number: INC000000001062\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-13T18:36:38.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "1168", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000001067", "status": "Cancelled", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: OtrasEmpresarial\nAplicativo o equipo afectado: Impresora\nRuta por la que ingresa: PRUEBA - Error\nTelefonos de afectado: 123456\nUbicación del afectado  (Sede y piso): PRUEBA - Error\nNombre completo del afectado: PRUEBA - Error\nUsuario con el que ingresó: PRUEBA - Error\nDescripción del error: PRUEBA - Error\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 1087\\nDWP_SRInstanceID: AGGEAEH8A71LBAS9WQ7DS9WQ7DIYTC\\nIncident Number: INC000000001067\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-16T13:49:00.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "1087", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000001068", "status": "Cancelled", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: OtrasEmpresarial\nAplicativo o equipo afectado: Impresora\nRuta por la que ingresa: PRUEBA - Error\nTelefonos de afectado: 123456\nUbicación del afectado  (Sede y piso): PRUEBA - Error\nNombre completo del afectado: PRUEBA - Error\nUsuario con el que ingresó: PRUEBA - Error\nDescripción del error: PRUEBA - Error\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: Si\nMedio de Ingreso PQRS: Derechos de petición\n¿Cuál es el numero de la PQRS?: 1234556", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 1089\\nDWP_SRInstanceID: AGGEAEH8A71LBAS9WRPYS9WRPYJ7B5\\nIncident Number: INC000000001068\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-16T13:49:31.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "1089", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000001069", "status": "Cancelled", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: OtrasEmpresarial\nAplicativo o equipo afectado: Bloqueo o configuración de acceso - MFA\nRuta por la que ingresa: PRUEBA - Error\nTelefonos de afectado: 123456\nUbicación del afectado  (Sede y piso): PRUEBA - Error\nNombre completo del afectado: PRUEBA - Error\nUsuario con el que ingresó: PRUEBA - Error\nDescripción del error: PRUEBA - Error\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: Si\nMedio de Ingreso PQRS: Derechos de petición\n¿Cuál es el numero de la PQRS?: 1234556", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 1090\\nDWP_SRInstanceID: AGGEAEH8A71LBAS9WRRJS9WRRJJ78Q\\nIncident Number: INC000000001069\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-16T13:50:07.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "1090", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000001070", "status": "Cancelled", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: OtrasEmpresarial\nAplicativo o equipo afectado: Bloqueo o configuración de acceso - MFA\nRuta por la que ingresa: PRUEBA - Error\nTelefonos de afectado: 123456\nUbicación del afectado  (Sede y piso): PRUEBA - Error\nNombre completo del afectado: PRUEBA - Error\nUsuario con el que ingresó: PRUEBA - Error\nDescripción del error: PRUEBA - Error\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 1091\\nDWP_SRInstanceID: AGGEAEH8A71LBAS9WRRTS9WRRTJ7O9\\nIncident Number: INC000000001070\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-16T13:50:24.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "1091", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000001073", "status": "Cancelled", "description": "Cotizador - Salud y PAC", "detailed_description": "Aplicativo Afectado: CotizadorSaludPAC\nProducto/ Proceso: Nuevo\nProducto: Salud Colectivo\nCotización: 123456\nPoliza: \nRadicado y/o poliza: 12345\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: Si\n¿Su inconveniente es para la expedición de una póliza con beneficiario oneroso?: No\nUsuario afectado: PRUEBA\nCelular del afectado: 12345\nRuta por la que ingresa: PRUEBA\nDescripción del error: PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 1099\\nDWP_SRInstanceID: AGGEAEH8A71LBAS9WSNRS9WSNRJQSR\\nIncident Number: INC000000001073\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-16T14:15:45.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "1099", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000001076", "status": "Cancelled", "description": "Cotizador - Salud y PAC", "detailed_description": "Aplicativo Afectado: CotizadorSaludPAC\nProducto/ Proceso: Nuevo\nProducto: Salud Colectivo\nCotización: 123456\nPoliza: \nRadicado y/o poliza: 12345\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: No\n¿Su inconveniente es para la expedición de una póliza con beneficiario oneroso?: No\nUsuario afectado: PRUEBA\nCelular del afectado: 12345\nRuta por la que ingresa: PRUEBA\nDescripción del error: PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: Si\nMedio de Ingreso: Derechos de petición\n¿Cuál es el numero de la PQRS?: 123456", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 1302\\nDWP_SRInstanceID: AGGEAEH8A71LBAS9WSYMS9WSYMJTWD\\nIncident Number: INC000000001076\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-16T14:16:20.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "1302", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000001082", "status": "Cancelled", "description": "Cotizador - Salud y PAC", "detailed_description": "Aplicativo Afectado: CotizadorSaludPAC\nProceso: Nuevo\nProducto: PAC\nCotización: 12345\nPoliza: \nRadicado y/o poliza: 12345\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: No\n¿Su inconveniente es para la expedición de una póliza de un amparo automático?: No\nUsuario afectado: PRUEBA\nCelular del afectado: 123456\nRuta por la que ingresa: PRUEBA\nDescripción del error: PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 1314\\nDWP_SRInstanceID: AGGEAEH8A71LBAS9WUC0S9WUC00H0E\\nIncident Number: INC000000001082\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-16T14:39:51.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "1314", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000001083", "status": "Cancelled", "description": "Cotizador - Salud y PAC", "detailed_description": "Aplicativo Afectado: CotizadorSaludPAC\nProceso: Modificación\nProducto: PAC\nCotización: \nPoliza: 1234567\nRadicado y/o poliza: 12345\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: No\n¿Su inconveniente es para la expedición de una póliza de un amparo automático?: No\nUsuario afectado: PRUEBA\nCelular del afectado: 123456\nRuta por la que ingresa: PRUEBA\nDescripción del error: PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 1316\\nDWP_SRInstanceID: AGGEAEH8A71LBAS9WUESS9WUES0I0W\\nIncident Number: INC000000001083\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-16T14:41:38.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "1316", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000001084", "status": "Cancelled", "description": "Cotizador - Salud y PAC", "detailed_description": "Aplicativo Afectado: CotizadorSaludPAC\nProceso: Modificación\nProducto: Juvenil\nCotización: \nPoliza: 1234567\nRadicado y/o poliza: 12345\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: No\n¿Su inconveniente es para la expedición de una póliza de un amparo automático?: No\nUsuario afectado: PRUEBA\nCelular del afectado: 123456\nRuta por la que ingresa: PRUEBA\nDescripción del error: PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 1317\\nDWP_SRInstanceID: AGGEAEH8A71LBAS9WUFOS9WUFO0IU9\\nIncident Number: INC000000001084\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-16T14:41:54.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "1317", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000001085", "status": "Cancelled", "description": "Cotizador - Salud y PAC", "detailed_description": "Aplicativo Afectado: CotizadorSaludPAC\nProceso: Modificación\nProducto: Surenta Colectivo\nCotización: \nPoliza: 1234567\nRadicado y/o poliza: 12345\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: No\n¿Su inconveniente es para la expedición de una póliza de un amparo automático?: No\nUsuario afectado: PRUEBA\nCelular del afectado: 123456\nRuta por la que ingresa: PRUEBA\nDescripción del error: PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 1318\\nDWP_SRInstanceID: AGGEAEH8A71LBAS9WUGDS9WUGD0IYW\\nIncident Number: INC000000001085\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-16T14:42:08.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "1318", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000001086", "status": "Cancelled", "description": "Cotizador - Salud y PAC", "detailed_description": "Aplicativo Afectado: CotizadorSaludPAC\nProceso: Modificación\nProducto: Surenta Familiar\nCotización: \nPoliza: 1234567\nRadicado y/o poliza: 12345\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: No\n¿Su inconveniente es para la expedición de una póliza de un amparo automático?: No\nUsuario afectado: PRUEBA\nCelular del afectado: 123456\nRuta por la que ingresa: PRUEBA\nDescripción del error: PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 1319\\nDWP_SRInstanceID: AGGEAEH8A71LBAS9WUG7S9WUG70JFV\\nIncident Number: INC000000001086\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-16T14:42:22.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "1319", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000001088", "status": "Cancelled", "description": "Cotizador - Salud y PAC", "detailed_description": "Aplicativo Afectado: CotizadorSaludPAC\nProducto/ Proceso: Modificación\nProducto: Salud Familiar\nCotización: \nPoliza: 123456\nRadicado y/o poliza: 12345\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: No\n¿Su inconveniente es para la expedición de una póliza con beneficiario oneroso?: No\nUsuario afectado: PRUEBA\nCelular del afectado: 12345\nRuta por la que ingresa: PRUEBA\nDescripción del error: PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: Si\nMedio de Ingreso: Derechos de petición\n¿Cuál es el numero de la PQRS?: 123456", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 1321\\nDWP_SRInstanceID: AGGEAEH8A71LBAS9WU15S9WU1506UM\\nIncident Number: INC000000001088\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-16T14:45:33.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "1321", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000001106", "status": "Closed", "description": "<PERSON><PERSON>", "detailed_description": "Aplicativo Afectado: FirmaRemota\nSolución: Autos\nNúmero de cotización: 12345\nUsuario afectado: Maripoda\nRuta por la que ingresa: hhttpp//:firmaremora.com\nDescripción del error: <PERSON><PERSON>, su ayuda por favor con el error adjunto en Firma Remota, grac<PERSON>.\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: CPU\\nClosure Product Category Tier1: API\\nResolution Category Tier 2: AUMENTO\\nClosure Product Category Tier2: Empresariales\\nClosure Product Category Tier3: Mascotas\\nDWP_SRID: 1236\\nDWP_SRInstanceID: AGGBNJMPI2BNYAS9X19GS9X19GWSH3\\nIncident Number: INC000000001106\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: API\\nCategorization Tier 3: Bloqueos-Lentitudes\\nProduct Categorization Tier 1: API\\nProduct Categorization Tier 3: Mascotas\\nProduct Categorization Tier 2: Empresariales\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:26:30.000+0000", "create_date": "2024-09-16T20:58:23.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Se restablece la operatividad de la aplicación.", "resolution_method": null, "dwp_number": "1236", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Prueba de adjunto", "last_modified_date": "2024-09-24T20:50:47.000+0000"}}, {"incident_id": "INC000000001108", "status": "Cancelled", "description": "Cotizador- Empresarial", "detailed_description": "Aplicativo Afectado: CotizadorEmpresarial\nTipo de falla: Transacción en proceso\nProducto / Proceso: Nuevo\nProducto: Agentes de Carga\nUsuario afectado: Maripoda\nCelular del afectado: 123456\nRuta por la que ingresa: httpp;cotizador.com\nCotización: 12345\nPoliza: \nRadicado y/o poliza: 12345\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: No\n¿La póliza tiene oneroso?: No\nDescripción del error: Buen día, su ayuda por favor con el error adjunto en la cotización 12345, gracias.\n¿Cuántas personas hay afectadas?: 1 persona¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Aplicaciones Transversales\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Otros\\nClosure Product Category Tier3: O<PERSON>s\\nDWP_SRID: 1237\\nDWP_SRInstanceID: AGGBNJMPI2BNYAS9X34BS9X34BXG2B\\nIncident Number: INC000000001108\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-16T21:38:47.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "1237", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "<PERSON><PERSON>, pruba comentario enmascarado.", "last_modified_date": "2024-09-20T21:09:30.000+0000"}}, {"incident_id": "INC000000000962", "status": "Cancelled", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: OtrasEmpresarial\nAplicativo o equipo afectado: Bloqueo o configuración de acceso - MFA\nRuta por la que ingresa: PRUEBA - Error\nTelefonos de afectado: 123456\nUbicación del afectado  (Sede y piso): PRUEBA - Error\nNombre completo del afectado: PRUEBA - Error\nUsuario con el que ingresó: PRUEBA - Error\nDescripción del error: PRUEBA - Error\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: Si\nMedio de Ingreso PQRS: Derechos de petición\n¿Cuál es el numero de la PQRS?: 1234", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 1092\\nDWP_SRInstanceID: AGGEAEH8A71LBAS9WRU9S9WRU9J81T\\nIncident Number: INC000000000962\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-16T13:52:18.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "1092", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000000964", "status": "Cancelled", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: OtrasEmpresarial\nAplicativo o equipo afectado: OtrosPRUEBA - Error\nRuta por la que ingresa: PRUEBA - Error\nTelefonos de afectado: 123456\nUbicación del afectado  (Sede y piso): PRUEBA - Error\nNombre completo del afectado: PRUEBA - Error\nUsuario con el que ingresó: PRUEBA - Error\nDescripción del error: PRUEBA - Error\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: Si\nMedio de Ingreso PQRS: Derechos de petición\n¿Cuál es el numero de la PQRS?: 1234", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 1094\\nDWP_SRInstanceID: AGGEAEH8A71LBAS9WRW4S9WRW4J8TV\\nIncident Number: INC000000000964\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-16T13:54:50.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "1094", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000000965", "status": "Cancelled", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: OtrasEmpresarial\nAplicativo o equipo afectado: OtrosPRUEBA - Error\nRuta por la que ingresa: PRUEBA - Error\nTelefonos de afectado: 123456\nUbicación del afectado  (Sede y piso): PRUEBA - Error\nNombre completo del afectado: PRUEBA - Error\nUsuario con el que ingresó: PRUEBA - Error\nDescripción del error: PRUEBA - Error\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 1095\\nDWP_SRInstanceID: AGGEAEH8A71LBAS9WRZVS9WRZVJ98M\\nIncident Number: INC000000000965\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-16T13:55:17.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "1095", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000000973", "status": "Cancelled", "description": "Cotizador - Salud y PAC", "detailed_description": "Aplicativo Afectado: CotizadorSaludPAC\nProceso: Nuevo\nProducto: Salud Familiar\nCotización: 12345\nPoliza: \nRadicado y/o poliza: 12345\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: No\n¿Su inconveniente es para la expedición de una póliza de un amparo automático?: No\nUsuario afectado: PRUEBA\nCelular del afectado: 123456\nRuta por la que ingresa: PRUEBA\nDescripción del error: PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 1305\\nDWP_SRInstanceID: AGGEAEH8A71LBAS9WTYRS9WTYR0ER4\\nIncident Number: INC000000000973\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-16T14:38:34.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "1305", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000000975", "status": "Cancelled", "description": "Cotizador - Salud y PAC", "detailed_description": "Aplicativo Afectado: CotizadorSaludPAC\nProceso: Nuevo\nProducto: Salud Colectivo\nCotización: 12345\nPoliza: \nRadicado y/o poliza: 12345\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: No\n¿Su inconveniente es para la expedición de una póliza de un amparo automático?: No\nUsuario afectado: PRUEBA\nCelular del afectado: 123456\nRuta por la que ingresa: PRUEBA\nDescripción del error: PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 1308\\nDWP_SRInstanceID: AGGEAEH8A71LBAS9WUAMS9WUAM0FWZ\\nIncident Number: INC000000000975\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-16T14:38:51.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "1308", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000000977", "status": "Cancelled", "description": "Cotizador - Salud y PAC", "detailed_description": "Aplicativo Afectado: CotizadorSaludPAC\nProceso: Nuevo\nProducto: Surenta Familiar\nCotización: 12345\nPoliza: \nRadicado y/o poliza: 12345\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: No\n¿Su inconveniente es para la expedición de una póliza de un amparo automático?: No\nUsuario afectado: PRUEBA\nCelular del afectado: 123456\nRuta por la que ingresa: PRUEBA\nDescripción del error: PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 1310\\nDWP_SRInstanceID: AGGEAEH8A71LBAS9WUAZS9WUAZ0GHK\\nIncident Number: INC000000000977\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-16T14:39:06.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "1310", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000000978", "status": "Cancelled", "description": "Cotizador - Salud y PAC", "detailed_description": "Aplicativo Afectado: CotizadorSaludPAC\nProceso: Nuevo\nProducto: Surenta Colectivo\nCotización: 12345\nPoliza: \nRadicado y/o poliza: 12345\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: No\n¿Su inconveniente es para la expedición de una póliza de un amparo automático?: No\nUsuario afectado: PRUEBA\nCelular del afectado: 123456\nRuta por la que ingresa: PRUEBA\nDescripción del error: PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 1311\\nDWP_SRInstanceID: AGGEAEH8A71LBAS9WUB7S9WUB70GN4\\nIncident Number: INC000000000978\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-16T14:39:22.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "1311", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000000979", "status": "Cancelled", "description": "Cotizador - Salud y PAC", "detailed_description": "Aplicativo Afectado: CotizadorSaludPAC\nProceso: Nuevo\nProducto: Juvenil\nCotización: 12345\nPoliza: \nRadicado y/o poliza: 12345\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: No\n¿Su inconveniente es para la expedición de una póliza de un amparo automático?: No\nUsuario afectado: PRUEBA\nCelular del afectado: 123456\nRuta por la que ingresa: PRUEBA\nDescripción del error: PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 1313\\nDWP_SRInstanceID: AGGEAEH8A71LBAS9WUBWS9WUBW0GW9\\nIncident Number: INC000000000979\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-16T14:39:37.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "1313", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000000987", "status": "Cancelled", "description": "AR - Centro de servicios (Chat, SS)", "detailed_description": "Prueba Certificación OLAS", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 1332\\nIncident Number: INC000000000987\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000303;1000000478;'maripoda';", "last_modified_date": "2025-02-25T22:10:56.000+0000", "create_date": "2024-09-16T15:32:28.000+0000", "priority": "Medium", "impact": "3-Moderate/Limited", "urgency": "3-Medium", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO):", "resolution_method": null, "dwp_number": "1332", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000001244", "status": "Cancelled", "description": "Group Center Empresarial", "detailed_description": "Aplicativo Afectado: GroupCenterEmpresarial\nTipo de falla: Transacción en proceso\nRuta por la que ingresa: PRUEBA\nProducto o solución afectada: PRUEBA\nPóliza: 12345\nOperación: PRUEBA\nUsuario afectado: PRUEBA\nContacto del usuario: 12345\nNombre de la oficina: PRUEBA\nCotización: 12345\n¿La póliza tiene oneroso?: Si\nDescripción del error: PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Aplicaciones Transversales\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Otros\\nClosure Product Category Tier3: O<PERSON>s\\nDWP_SRID: 1502\\nDWP_SRInstanceID: AGGBNJMPI2BNYASKETB0SKETB01E1X\\nIncident Number: INC000000001244\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-20T21:59:23.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "1502", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000001174", "status": "Closed", "description": "Contact Empresarial", "detailed_description": "Aplicativo Afectado: ContactEmpresarial\nRuta por la que ingresa: .\nPóliza: 1234456\nOperación: PRUEBA\nUsuario afectado: PRUEBA\nContacto del usuario: 12345678\nDescripción del error: PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: Si\nMedio de Ingreso PQRS: Entes de control\n¿Cuál es el numero de la PQRS?: 1234", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: NO EXITOSO\\nClosure Product Category Tier1: Billing Center\\nResolution Category Tier 2: CONTACTO\\nClosure Product Category Tier2: Empresariales\\nClosure Product Category Tier3: Empresariales\\nDWP_SRID: 1427\\nDWP_SRInstanceID: AGGEAEH8A71LBASK1KBWSK1KBW9TIZ\\nIncident Number: INC000000001174\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: Aplicaciones Canal Asesor\\nCategorization Tier 2: Contact Empresarial\\nCategorization Tier 3: Bloqueos-Lentitudes\\nProduct Categorization Tier 1: API\\nProduct Categorization Tier 3: Mascotas\\nProduct Categorization Tier 2: Empresariales\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-04-22T20:49:28.000+0000", "create_date": "2024-09-24T13:28:33.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "1427", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Buen día \nSe resuelve en prueba\nFeliz día", "last_modified_date": "2024-09-25T16:03:01.000+0000"}}, {"incident_id": "INC000000001190", "status": "Cancelled", "description": "Contact Empresarial", "detailed_description": "Aplicativo Afectado: ContactEmpresarial\nRuta por la que ingresa: PRUEBA\nPóliza: 1234567\nOperación: PRUEBA\nUsuario afectado: PRUEBA\nContacto del usuario: 123456\nDescripción del error: PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nClosure Product Category Tier1: Aplicaciones Canal Asesor\\nClosure Product Category Tier2: WIDGET\\nClosure Product Category Tier3: pendiente\\nDWP_SRID: 1442\\nDWP_SRInstanceID: AGGEAEH8A71LBASK2HHVSK2HHVNXFP\\nIncident Number: INC000000001190\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: Servicios de Tecnologia\\nCategorization Tier 2: Gestion ITSM\\nCategorization Tier 3: Hallazgo en Estabilización\\nProduct Categorization Tier 1: Aplicaciones Canal Asesor\\nProduct Categorization Tier 3: pendiente\\nProduct Categorization Tier 2: WIDGET\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-18T21:05:19.000+0000", "create_date": "2024-09-24T21:52:03.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "1442", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "prueba nueva nota pública.", "last_modified_date": "2024-09-25T21:37:34.000+0000"}}, {"incident_id": "INC000000001191", "status": "Cancelled", "description": "Prueba", "detailed_description": "Aplicativo Afectado: ContactEmpresarial\nRuta por la que ingresa: PRUEBA\nPóliza: 1234567\nOperación: PRUEBA\nUsuario afectado: PRUEBA\nContacto del usuario: 123456\nDescripción del error: PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-031F7424892011EF89D95E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nClosure Product Category Tier2: Incidente\\nHPD_CI: Cotizador de Autos\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 1443\\nDWP_SRInstanceID: AGGEAEH8A71LBASK2H74SK2H74NZ0I\\nIncident Number: INC000000001191\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC (Pruebas)\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-18T21:02:34.000+0000", "create_date": "2024-09-24T21:53:43.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "1443", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Buen día, su ayuda por favor escalando al grupo XXX y asignado a XXX, gracias.", "last_modified_date": "2025-01-20T19:49:27.000+0000"}}, {"incident_id": "INC000000001192", "status": "Cancelled", "description": "Contact Empresarial", "detailed_description": "Aplicativo Afectado: ContactEmpresarial\nRuta por la que ingresa: PRUEBA\nPóliza: 12345\nOperación: PRUEBA\nUsuario afectado: PRUEBA\nContacto del usuario: 123456\nDescripción del error: PRUEBA - Adjunto\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 1444\\nDWP_SRInstanceID: AGGEAEH8A71LBASK2HRASK2HRAOBCC\\nIncident Number: INC000000001192\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC (Pruebas)\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-24T22:00:14.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "1444", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Prueba nota pública.", "last_modified_date": "2024-09-27T19:53:09.000+0000"}}, {"incident_id": "INC000000001285", "status": "Cancelled", "description": "Contact Empresarial", "detailed_description": "Aplicativo Afectado: ContactEmpresarial\nRuta por la que ingresa: PRUEBA\nPóliza: 12345\nOperación: PRUEBA\nUsuario afectado: PRUEBA\nContacto del usuario: 123456\nDescripción del error: PRUEBA - Adjunto\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 1566\\nDWP_SRInstanceID: AGGBNJMPI2BNYASK3V9SSK3V9SUKBR\\nIncident Number: INC000000001285\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC (Pruebas)\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-25T19:31:07.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "1566", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000001630", "status": "Cancelled", "description": "Contact Empresarial", "detailed_description": "Aplicativo Afectado: ContactEmpresarial\nRuta por la que ingresa: .\nPóliza: 1234456\nOperación: PRUEBA\nUsuario afectado: PRUEBA\nContacto del usuario: 12345678\nDescripción del error: PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: Si\nMedio de Ingreso PQRS: Entes de control\n¿Cuál es el numero de la PQRS?: 1234", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 1926\\nDWP_SRInstanceID: AGGB2CYFGM667ASKQRYTSKQRYTQFKQ\\nIncident Number: INC000000001630\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC (Pruebas)\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-10-02T18:43:10.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "1926", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "<PERSON><PERSON>, estoy disponible para recibir la atención muchas gracias.", "last_modified_date": "2024-10-02T19:31:20.000+0000"}}, {"incident_id": "INC000000001809", "status": "Cancelled", "description": "Cotizador- Empresarial", "detailed_description": "Aplicativo Afectado: CotizadorEmpresarial\nTipo de falla: Transacción en proceso\nProducto / Proceso: Nuevo\nProducto: Embarcaciones de recreo\nUsuario afectado: Maripoda\nCelular del afectado: 123456789\nRuta por la que ingresa: No Aplica\nCotización: 123456795678\nPoliza: \nRadicado y/o poliza: 123456790\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: No\n¿La póliza tiene oneroso?: No\nDescripción del error: Hola, su ayuda por favor con el siguiente error que se muestra en el cotizador para la cotización 537950945, gracias. Adjunto error.\n¿Cuántas personas hay afectadas?: 1 persona¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 2122\\nDWP_SRInstanceID: AGGH4ZM7BVS1QASLAEJISLAEJIAC17\\nIncident Number: INC000000001809\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC (Pruebas)\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-10-07T19:47:09.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "2122", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Prueba con operación de motivo del estado + actividad.", "last_modified_date": "2024-10-07T20:31:31.000+0000"}}, {"incident_id": "INC000000001812", "status": "Cancelled", "description": "Cotizador - Salud y PAC", "detailed_description": "Aplicativo Afectado: CotizadorSaludPAC\nProducto/ Proceso: Nuevo\nProducto: Salud Familiar\nCotización: 123445\nPoliza: \nRadicado y/o poliza: 12345\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: No\n¿Su inconveniente es para la expedición de una póliza de un amparo automático?: Si\nUsuario afectado: PRUEBA\nCelular del afectado: 12345\nRuta por la que ingresa: PRUEBA\nDescripción del error: PRUEBA - Oneroso/Amparo automático\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 2023\\nDWP_SRInstanceID: AGGEM7T2FGTSWASLAHGHSLAHGH5IOJ\\nIncident Number: INC000000001812\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC (Pruebas)\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-10-07T20:50:58.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "2023", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Nota privada", "last_modified_date": "2024-10-09T22:19:20.000+0000"}}, {"incident_id": "INC000000001813", "status": "Cancelled", "description": "Cotizador - Salud y PAC", "detailed_description": "Aplicativo Afectado: CotizadorSaludPAC\nProducto/ Proceso: Cancelación\nProducto: Juvenil\nCotización: \nPoliza: 1234567\nRadicado y/o poliza: 12345\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: No\n¿Su inconveniente es para la expedición de una póliza de un amparo automático?: Si\nUsuario afectado: PRUEBA\nCelular del afectado: 12345\nRuta por la que ingresa: PRUEBA\nDescripción del error: PRUEBA - Oneroso/Amparo automático\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 2024\\nDWP_SRInstanceID: AGGEM7T2FGTSWASLAH4NSLAH4N51DJ\\nIncident Number: INC000000001813\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC (Pruebas)\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-10-07T20:55:15.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Prueba resolución", "resolution_method": null, "dwp_number": "2024", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000001911", "status": "Cancelled", "description": "Cotizador- Empresarial", "detailed_description": "Aplicativo Afectado: CotizadorEmpresarial\nTipo de falla: Transacción en proceso\nProducto / Proceso: Nuevo\nProducto: Agentes de Carga\nUsuario afectado: Maripoda\nCelular del afectado: 123456789\nRuta por la que ingresa: No Aplica\nCotización: 1234567\nPoliza: \nRadicado y/o poliza: 1234567\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: No\n¿La póliza tiene oneroso?: No\nDescripción del error: Ho<PERSON>, su ayuda por favor con el siguiente error que se muestra en el cotizador para la cotización 1234567, gracias. Adjunto error.\n¿Cuántas personas hay afectadas?: 1 persona¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 2114\\nDWP_SRInstanceID: AGGH4ZM7BVS1QASKZVBKSKZVBKXTC4\\nIncident Number: INC000000001911\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC (Pruebas)\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-10-07T16:29:52.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente):  <PERSON><PERSON><PERSON> usuario, el error en el cotizador se debe a que no esta agregando el parametro correcto.\nSolución: Agregar el parametro correcto. \nCausa Raíz (Identificada/Sin identificar):  Sin identificar\nConfirmar operatividad del usuario Afectado (SI/NO):  SI", "resolution_method": null, "dwp_number": "2114", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Buen día señor usuario, nos intentamos comunicar con usted pero no fue posible el contacto. Nos comunicaremos de nuevo el 8 de octubre en las horas de la mañana para darle gestión a su caso, muchas gracias.", "last_modified_date": "2024-10-07T16:38:22.000+0000"}}, {"incident_id": "INC000000001913", "status": "Cancelled", "description": "Cotizador- Empresarial", "detailed_description": "Aplicativo Afectado: CotizadorEmpresarial\nTipo de falla: Transacción en proceso\nProducto / Proceso: Nuevo\nProducto: Agentes de Carga\nUsuario afectado: Maripoda\nCelular del afectado: 123456789\nRuta por la que ingresa: No Aplica\nCotización: 1234568\nPoliza: \nRadicado y/o poliza: 1234568\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: No\n¿La póliza tiene oneroso?: No\nDescripción del error: Ho<PERSON>, su ayuda por favor con el siguiente error que se muestra en el cotizador para la cotización 1234568, gracias. Adjunto error.\n¿Cuántas personas hay afectadas?: 1 persona¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 2115\\nDWP_SRInstanceID: AGGH4ZM7BVS1QASKZWIJSKZWIJY084\\nIncident Number: INC000000001913\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC (Pruebas)\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-10-07T16:53:50.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "2115", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000001916", "status": "Cancelled", "description": "Cotizador- Empresarial", "detailed_description": "Aplicativo Afectado: CotizadorEmpresarial\nTipo de falla: Transacción en proceso\nProducto / Proceso: Nuevo\nProducto: Agentes de Carga\nUsuario afectado: Maripoda\nCelular del afectado: 123456789\nRuta por la que ingresa: No Aplica\nCotización: 1234568\nPoliza: \nRadicado y/o poliza: 1234568\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: No\n¿La póliza tiene oneroso?: No\nDescripción del error: Ho<PERSON>, su ayuda por favor con el siguiente error que se muestra en el cotizador para la cotización 1234568, gracias. Adjunto error.\n¿Cuántas personas hay afectadas?: 1 persona¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 2118\\nDWP_SRInstanceID: AGGH4ZM7BVS1QASKZX4USKZX4UY76X\\nIncident Number: INC000000001916\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC (Pruebas)\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-10-07T17:18:33.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "2118", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "PRUEBA", "last_modified_date": "2024-10-07T19:13:11.000+0000"}}, {"incident_id": "INC000000001918", "status": "Closed", "description": "Acceso único de solicitudes AUS", "detailed_description": "Aplicativo Afectado: AccesoUnicoSolicitudesAUS\nProducto/ Proceso: Movilidad AUS\nProducto: \nRadicado y/o poliza: 12345678\nUsuario afectado: Maripoda\nCelular del afectado: 306882729\nRuta por la que ingresa: No Aplica\nDescripción del error: PRUEBA - Omitir.\n<PERSON><PERSON>, su ayuda por favor con el siguiente error.\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: SOLUCIONADO SIN ACCIONES\\nClosure Product Category Tier1: AUS - Acceso Unico de Solicitudes\\nResolution Category Tier 2: EXCLUSIVO GESTION DE INCIDENTES\\nClosure Product Category Tier2: AUS - Acceso Unico de Solicitudes\\nClosure Product Category Tier3: AUS - Acceso Unico de Solicitudes\\nDWP_SRID: 2120\\nDWP_SRInstanceID: AGGH4ZM7BVS1QASLADQXSLADQXZYC5\\nIncident Number: INC000000001918\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: AUS - Acceso Unico de Solicitudes\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: AUS - Acceso Unico de Solicitudes\\nProduct Categorization Tier 3: AUS - Acceso Unico de Solicitudes\\nProduct Categorization Tier 2: AUS - Acceso Unico de Solicitudes\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:13:58.000+0000", "create_date": "2024-10-07T19:39:36.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "* Agrupador del Error: 0. <PERSON><PERSON><PERSON> rápido |\n* Causa del Error: 10.Solucionado sin acciones - Fue un error Momentaneo  | \n* Proceso del Error: 61. Consulta |\n* HU Raizal / Mejora: N/A | \n* Estado Raizal: 6. N/A | \n* Responsable Solucion: 1. TI | \n* Diagnostico: prueba - omitir.\nhola, su ayuda por favor con el siguiente error.\n¿cuántas personas hay afectadas?: 1 persona\n¿el reporte surge de una pqrs de salesforce?: no\n | \n* Accion Ejecutada: Sin intervención | \n* Descripcion de Solucion: Se procede con el cierre, ya que no se tiene información detallada, ni adjuntos para la revisión. |\n* Confirmar operatividad del usuario Afectado: Si |\n* ID Formulario de Solicitud de Credenciales: N/A |\n* OC Acceso a PDN- (PAM): N/A |\n* Causa Raiz(Identificada/Sin Identificar):Identidicada |", "resolution_method": null, "dwp_number": "2120", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000001957", "status": "Closed", "description": "Acceso único de solicitudes AUS", "detailed_description": "Aplicativo Afectado: AccesoUnicoSolicitudesAUS\nProducto/ Proceso: Movilidad AUS\nProducto: \nUsuario afectado: Maripoda\nCelular del afectado: 12345\nRuta por la que ingresa: No Aplica\nDescripción del error: PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: Si\nMedio de Ingreso: Derechos de petición\n¿Cuál es el numero de la PQRS?: 122345", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 2064\\nDWP_SRInstanceID: AGGEM7T2FGTSWASLF0FFSLF0FF9YWD\\nIncident Number: INC000000001957\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC (Pruebas)\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-10-10T14:45:33.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Prueba reapertura", "resolution_method": null, "dwp_number": "2064", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "<PERSON><PERSON>, nota publica", "last_modified_date": "2024-10-10T14:51:26.000+0000"}}, {"incident_id": "INC000000001973", "status": "Closed", "description": "Acceso único de solicitudes AUS", "detailed_description": "Aplicativo Afectado: AccesoUnicoSolicitudesAUS\nProducto/ Proceso: Movilidad AUS\nProducto: \nRadicado y/o poliza: 12345\nUsuario afectado: 12345\nCelular del afectado: 12345\nRuta por la que ingresa: PRUEBA\nDescripción del error: PRUEBA CONTEO ANS - Reapertura\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 2218\\nDWP_SRInstanceID: AGGH4ZM7BVS1QASLHHA3SLHHA39ID0\\nIncident Number: INC000000001973\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC (Pruebas)\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-10-11T15:29:58.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Prueba", "resolution_method": null, "dwp_number": "2218", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000001974", "status": "Closed", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: OtrasEmpresarial\nAplicativo o equipo afectado: VPN\nRuta por la que ingresa: PRUEBA\nTelefonos de afectado: 1234567\nUbicación del afectado  (Sede y piso): PRUEBA\nNombre completo del afectado: PRUEBA\nUsuario con el que ingresó: PRUEBA\nDescripción del error: PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 2219\\nDWP_SRInstanceID: AGGH4ZM7BVS1QASLHHEGSLHHEG9IYB\\nIncident Number: INC000000001974\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC (Pruebas)\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-10-11T15:31:55.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Prueba de ANS con reapertura", "resolution_method": null, "dwp_number": "2219", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000001976", "status": "Cancelled", "description": "Somos Sura - No puede ingresar", "detailed_description": "Aplicativo o equipo afectado: Somos Sura\nDescripción del error o falla:  No puede ingresar a somos sura con su usuario de red, dice que hace poco realizó el cambio de clave.\nRuta por la que ingresa: Somos Sura\nOperación que estaba realizando:  Ingreso a https://www.sura.co/web/somos-sura\nNombre del afectado: <PERSON>\nUbicación (Sede y piso): Teletrabajo\nTeléfonos (Celular, fijo, ext.): 12345678\nNúmero de usuarios afectados: 1\nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos): Maripoda", "technical_info": "Associated Alarm: None\\nClosure Product Category Tier1: Aplicaciones Transversales\\nClosure Product Category Tier2: Somos Sura\\nClosure Product Category Tier3: Somos Sura\\nDWP_SRID: 2227\\nIncident Number: INC000000001976\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Somos Sura\\nCategorization Tier 3: No puede ingresar\\nProduct Categorization Tier 1: Aplicaciones Transversales\\nProduct Categorization Tier 3: Somos Sura\\nProduct Categorization Tier 2: Somos Sura\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000478;'maripoda';", "last_modified_date": "2025-02-15T23:13:12.000+0000", "create_date": "2024-10-11T20:02:29.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "2227", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000002266", "status": "Closed", "description": "prueba para asociar", "detailed_description": "prueba para asociar", "technical_info": "Associated Alarm: None\\nDirect Contact City: MEDELLIN\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Home Cotizador\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Home Cotizador\\nClosure Product Category Tier3: Home Cotizador\\nDWP_SRID: 2616\\nIncident Number: INC000000002266\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: Cotizador\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: Home Cotizador\\nProduct Categorization Tier 3: Home Cotizador\\nProduct Categorization Tier 2: Home Cotizador\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';'luisgupv';", "last_modified_date": "2025-02-16T01:29:50.000+0000", "create_date": "2024-10-23T16:04:42.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Prueba", "resolution_method": null, "dwp_number": "2616", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000002259", "status": "Cancelled", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: OtrasEmpresarial\nAplicativo o equipo afectado: OtrosPRUEBA\nRuta por la que ingresa: PRUEBA\nTelefonos de afectado: 123456\nUbicación del afectado  (Sede y piso): PRUEBA\nNombre completo del afectado: PRUEBA\nUsuario con el que ingresó: PRUEBA\nDescripción del error: PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 2605\\nDWP_SRInstanceID: AGGH4ZM7BVS1QASLSJMYSLSJMYEXI4\\nIncident Number: INC000000002259\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC (Pruebas)\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-10-23T00:36:58.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "PRUEBA", "resolution_method": null, "dwp_number": "2605", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": ".", "last_modified_date": "2024-10-23T19:43:21.000+0000"}}, {"incident_id": "INC000000002582", "status": "Cancelled", "description": "Asistente Virtual Asesor-AVA", "detailed_description": "Aplicativo Afectado: AsistenteVirtualAsesor_AVA\nUsuario Autenticación (DNI): PRUEBA\nCódigo de asesor: 12344\nRuta del menú por la que ingresa: PRUEBA\nPóliza: 1234567\nRamo: PRUEBA\nParámetros filtro de búsqueda: PRUEBA\nTeléfonos del afectado (Celular, Fijo, Ext): 12345\nDescripción del error: PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿Su inconveniente es con el ingreso AVA?: No\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: INFORMACION\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: RESTAURAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 3130\\nDWP_SRInstanceID: AGGH4ZM7BVS1QASMWT70SMWT70GHFS\\nIncident Number: INC000000002582\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC (Pruebas)\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-11-13T22:08:41.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): .", "resolution_method": null, "dwp_number": "3130", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "<PERSON><PERSON>, agregó adjunto de prue<PERSON>", "last_modified_date": "2024-11-20T16:55:08.000+0000"}}, {"incident_id": "INC000000002739", "status": "Cancelled", "description": "Modificación Resumen", "detailed_description": "Prueba", "technical_info": "Associated Alarm: None\\nClosure Product Category Tier1: PorChat Asesores\\nClosure Product Category Tier2: PorChat Asesores\\nClosure Product Category Tier3: PorChat Asesores\\nDWP_SRID: 3320\\nIncident Number: INC000000002739\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: PorChat Asesores\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: PorChat Asesores\\nProduct Categorization Tier 3: PorChat Asesores\\nProduct Categorization Tier 2: PorChat Asesores\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000478;'maripoda';", "last_modified_date": "2025-02-15T23:12:19.000+0000", "create_date": "2024-11-20T18:11:39.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "3320", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000002747", "status": "Cancelled", "description": "Asistente Virtual Asesor-AVA", "detailed_description": "Aplicativo Afectado: AsistenteVirtualAsesor_AVA\nUsuario Autenticación (DNI): PRUEBA\nCódigo de asesor: 12344\nRuta del menú por la que ingresa: PRUEBA\nPóliza: 1234567\nRamo: PRUEBA\nParámetros filtro de búsqueda: PRUEBA\nTeléfonos del afectado (Celular, Fijo, Ext): 12345\nDescripción del error: PRUEBA COMENTARIO ESPECIALISTA\n¿Cuántas personas hay afectadas?: 1 persona\n¿Su inconveniente es con el ingreso AVA?: No\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 3325\\nDWP_SRInstanceID: AGGH4ZM7BVS1QASNJUFOSNJUFOLEK1\\nIncident Number: INC000000002747\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC (Pruebas)\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-11-20T22:51:32.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Prueba resolución", "resolution_method": null, "dwp_number": "3325", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Prueba consolda", "last_modified_date": "2024-12-10T13:29:03.000+0000"}}, {"incident_id": "INC000000003049", "status": "Cancelled", "description": "Prueba - Consola", "detailed_description": "Prueba - Consola", "technical_info": "Associated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 3705\\nIncident Number: INC000000003049\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC (Pruebas)\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-12-12T13:05:57.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "3705", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Se cancela, por pruebas del hallazgo 158.", "last_modified_date": "2024-12-12T15:42:08.000+0000"}}, {"incident_id": "INC000000003052", "status": "Cancelled", "description": "Prueba Registros Caso", "detailed_description": "Prueba registros en Consola", "technical_info": "z1D_CI_FormName: AST:Application\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 3709\\nIncident Number: INC000000003052\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC (Pruebas)\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-12-12T15:54:01.000+0000", "priority": "High", "impact": "4-Minor/Localized", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "3709", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Prueba cancelación", "last_modified_date": "2024-12-12T16:00:51.000+0000"}}, {"incident_id": "INC000000003348", "status": "Cancelled", "description": "Aplicación - Sintoma", "detailed_description": "Hola, ejemplo.", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-03C2E7DA892011EF8A6C5E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: GESTION CON USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CONTACTO\\nClosure Product Category Tier2: Incidente\\nHPD_CI: Cotizador GWT\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 4072\\nIncident Number: INC000000003348\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC (Pruebas)\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000478;'maripoda';", "last_modified_date": "2025-03-01T05:00:00.000+0000", "create_date": "2025-01-13T15:44:47.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "<PERSON><PERSON>, su caso quedo resuelto.", "resolution_method": null, "dwp_number": "4072", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Se gestiono el caso con XXXX.", "last_modified_date": "2025-01-13T16:33:01.000+0000"}}, {"incident_id": "INC000000003318", "status": "Cancelled", "description": "ipsa bloqueo en phc", "detailed_description": "Aplicativo o equipo afectado: equipo\nDescripción del error o falla:usuario indica que no puede ingresar a computador le dice que las credenciales no son validas o esta bloqueado\nRuta por la que ingresa: equipo\nOperación que estaba realizando:inicio de sesion\nNombre del afectado:Incidentes,Gestión De,\nUbicación (Sede y piso): remoto\nTeléfonos (Celular, fijo, ext.): 3106270605\nNúmero de usuarios afectados:1\nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):marirusr\nAdjuntar Pantalla del error completa donde se observe el error.\n(Diligencie cada uno de estos campos cuando aplique).", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-02F7990E892011EF89B55E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nClosure Product Category Tier1: Cotizador Salud y PAC\\nClosure Product Category Tier2: Vida\\nHPD_CI: Cotizador Salud y PAC\\nClosure Product Category Tier3: Salud y PAC\\nDWP_SRID: 4046\\nIncident Number: INC000000003318\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: Aplicaciones Seguros\\nCategorization Tier 2: Cotizador\\nCategorization Tier 3: Bloqueos-Lentitudes\\nProduct Categorization Tier 1: Cotizador Salud y PAC\\nProduct Categorization Tier 3: Salud y PAC\\nProduct Categorization Tier 2: Vida\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000124;'maripoda';", "last_modified_date": "2025-02-15T23:50:32.000+0000", "create_date": "2025-01-08T20:25:36.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "4046", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Buenas tardes\nSe cancela ya que se creo el caso para pruebas", "last_modified_date": "2025-01-16T19:26:39.000+0000"}}, {"incident_id": "INC000000003407", "status": "Cancelled", "description": "Plantilla Estándar", "detailed_description": "Aplicativo o equipo afectado: \nDescripción del error o falla: \nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-03941252892011EF8A445E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nClosure Product Category Tier2: Incidente\\nHPD_CI: Claim Center\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 4120\\nIncident Number: INC000000003407\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC (Pruebas)\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000478;'maripoda';", "last_modified_date": "2025-02-18T21:01:26.000+0000", "create_date": "2025-01-11T01:51:47.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "4120", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Prueba", "last_modified_date": "2025-01-13T14:07:00.000+0000"}}, {"incident_id": "INC000000003433", "status": "Closed", "description": "Reporte de fallla", "detailed_description": "Aplicacion / Modulo afectado: ReportesDashboard\nTipo de error: Error en Reportes\nNombre Usuario Afectado: <PERSON>\nUsuario afectado: Maripoda\nContacto usuario afectado: **********\nDescripción del error: Buen día, espero que estén muy bien.\n\nSu ayuda por favor, en los reportes disponibles las peticiones se están mostrando duplicadas. Esta pasando en las sabanas de Abierto, <PERSON><PERSON>do y <PERSON>to. Por favor validar si esta pasando tambien en los demás tableros, gracias.", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-03D6EBA4892011EF8A7D5E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: REVISE MENSAJE DE ERROR\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: ADMINISTRACION BMC\\nClosure Product Category Tier2: Incidente\\nHPD_CI: Acompañamiento Especializado\\nClosure Product Category Tier3: Error en Reportes\\nDWP_SRID: 4180\\nDWP_SRInstanceID: AGGEM7T2FGTSWASQES8HSQES8H3TH6\\nIncident Number: INC000000003433\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: Servicios de Tecnologia\\nCategorization Tier 2: Gestion ITSM\\nCategorization Tier 3: Error en Reportes\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en Reportes\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;**********;'maripoda';", "last_modified_date": "2025-04-22T20:49:28.000+0000", "create_date": "2025-01-15T13:15:22.000+0000", "priority": "Medium", "impact": "3-Moderate/Limited", "urgency": "3-Medium", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Buen día,\n\nEspero se encuentren bien. Atendiendo la solicitud, nos permitimos informar que se ha realizado la revisión de los reportes, ajustando la duplicidad en aquellos donde correspondía. En el caso de reportes como el de seguimiento de incidentes, la duplicidad se genera porque se relacionan los tiempos de SLA asignados al caso. Por otra parte, en el reporte Sabana de tiempo efectivo por grupos, los registros se repiten debido a que muestran el paso del caso por cada grupo.\nLos demás reportes no presentan novedades de duplicidad. Por favor validar y notificar cualquier inquietud o novedad.", "resolution_method": null, "dwp_number": "4180", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nBuen día,\n\nEspero se encuentren bien. Atendiendo la solicitud, nos permitimos informar que se ha realizado la revisión de los reportes, ajustando la duplicidad en aquellos donde correspondía. En el caso de reportes como el de seguimiento de incidentes, la duplicidad se genera porque se relacionan los tiempos de SLA asignados al caso. Por otra parte, en el reporte Sabana de tiempo efectivo por grupos, los registros se repiten debido a que muestran el paso del caso por cada grupo.\nLos demás reportes no presentan novedades de duplicidad. Por favor validar y notificar cualquier inquietud o novedad.\n\nCategoría de resolución:\nSERVICIOS DE TECNOLOGIA.ADMINISTRACION BMC.REVISE MENSAJE DE ERROR", "last_modified_date": "2025-01-16T13:32:43.000+0000"}}, {"incident_id": "INC000000003380", "status": "Cancelled", "description": "Plantilla General", "detailed_description": "Aplicativo o equipo afectado: \nDescripción del error o falla: \nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-031F7424892011EF89D95E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nClosure Product Category Tier1: Global Web\\nClosure Product Category Tier2: Produccion\\nHPD_CI: Cotizador de Autos\\nClosure Product Category Tier3: Produccion\\nDWP_SRID: 4195\\nIncident Number: INC000000003380\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: Aplicaciones Seguros\\nCategorization Tier 2: Cotizador\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: Global Web\\nProduct Categorization Tier 3: Produccion\\nProduct Categorization Tier 2: Produccion\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000478;'maripoda';", "last_modified_date": "2025-02-18T21:01:27.000+0000", "create_date": "2025-01-15T22:39:21.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "4195", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000003357", "status": "Cancelled", "description": "Prueba Nombre Plantilla", "detailed_description": "Aplicativo Afectado: CotizadorMovilidad\nProceso: Nuevo\nCotización: 12345\nUsuario afectado: Maripoda\nCelular usuario afectado: 123456\nRuta por la que ingresa: 12345\nRadicado y/o poliza: \n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: No\nDescripción del error : PRUEBA - Hola, su ayuda el siguiente caso.\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-01FAEBE6892011EF88CD5E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Incidente\\nHPD_CI: <PERSON>ti<PERSON><PERSON>gar\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 4077\\nDWP_SRInstanceID: AGGH4ZM7BVS1QASQBKAGSQBKAGQ71P\\nIncident Number: INC000000003357\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC (Pruebas)\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-04-22T20:49:28.000+0000", "create_date": "2025-01-13T19:14:32.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente):\nSolución: prueba\nCausa Raíz (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO):", "resolution_method": null, "dwp_number": "4077", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: prueba", "last_modified_date": "2025-02-11T01:57:49.000+0000"}}, {"incident_id": "INC000000003426", "status": "Cancelled", "description": "Somos Sura - Bloqueos", "detailed_description": "Prueba", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-026B5B10892011EF89355E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: USUARIOS\\nClosure Product Category Tier1: Aplicaciones Transversales\\nResolution Category Tier 2: PROCESO\\nClosure Product Category Tier2: Somos Sura\\nHPD_CI: SomosSura\\nClosure Product Category Tier3: Somos Sura\\nDWP_SRID: 4176\\nIncident Number: INC000000003426\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Somos Sura\\nCategorization Tier 3: Bloqueos-Lentitudes\\nProduct Categorization Tier 1: Aplicaciones Transversales\\nProduct Categorization Tier 3: Somos Sura\\nProduct Categorization Tier 2: Somos Sura\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000478;'maripoda';", "last_modified_date": "2025-03-01T05:00:00.000+0000", "create_date": "2025-01-14T19:51:06.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Solución del caso.", "resolution_method": null, "dwp_number": "4176", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Se cancela el caso porque se esta gestionando con XXX, gracias.", "last_modified_date": "2025-01-14T20:27:33.000+0000"}}, {"incident_id": "INC000000003374", "status": "Closed", "description": "Petición Hija relacionada a la afectación mayor 3913", "detailed_description": "Aplicativo Afectado: AsistenteVirtualAsesor_AVA\nUsuario Autenticación (DNI): isabe<PERSON> ases<PERSON>: 72\nRuta del menú por la que ingresa: somossura\nPóliza: 231452432\nRamo: Re12\nParámetros filtro de búsqueda: 5342212\nTeléfonos del afectado (Celular, Fijo, Ext): 31783709\nDescripción del error: prueba para IM\n¿Cuántas personas hay afectadas?: 1 persona\n¿Su inconveniente es con el ingreso AVA?: No\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "HPD_CI_ReconID: OI-01CCC004892011EF88A65E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: CPU\\nClosure Product Category Tier1: Aplicaciones Transversales\\nResolution Category Tier 2: AUMENTO\\nClosure Product Category Tier2: Otros\\nHPD_CI: AVA - Asistente Virtual Asesores\\nClosure Product Category Tier3: Otros\\nDWP_SRID: 4218\\nDWP_SRInstanceID: AGGH4ZM7BVS1QASQF2DZSQF2DZRL4L\\nIncident Number: INC000000003374\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC (Pruebas)\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2025-01-15T20:12:40.000+0000", "priority": "High", "impact": "2-Significant/Large", "urgency": "2-High", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "PRUEBA", "resolution_method": null, "dwp_number": "4218", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nPRUEBA\n\nCategoría de resolución:\nAUMENTO DE CAPACIDAD DISPOSITIVOS.AUMENTO.CPU", "last_modified_date": "2025-02-06T05:11:26.000+0000"}}, {"incident_id": "INC000000003375", "status": "Cancelled", "description": "Petición Hija relacionada a la afectación mayor 4209", "detailed_description": "Pruebas en producción - Incidentes mayores", "technical_info": "HPD_CI_ReconID: OI-01FAEBE6892011EF88CD5E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: REINICIO\\nClosure Product Category Tier1: Coti<PERSON><PERSON> Hogar\\nResolution Category Tier 2: REINICIO\\nClosure Product Category Tier2: Propiedad\\nHPD_CI: Cotizador Hogar\\nClosure Product Category Tier3: Hogares Sura\\nDWP_SRID: 4219\\nDWP_SRInstanceID: AGGH4ZM7BVS1QASQF2NASQF2NAROO1\\nIncident Number: INC000000003375\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: Cotizador\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: Cotizador Hogar\\nProduct Categorization Tier 3: Hogares Sura\\nProduct Categorization Tier 2: Propiedad\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;'maripoda';", "last_modified_date": "2025-04-22T20:49:28.000+0000", "create_date": "2025-01-15T20:23:56.000+0000", "priority": "High", "impact": "2-Significant/Large", "urgency": "2-High", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente):\nSolución: prueba\nCausa Raíz (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO):", "resolution_method": null, "dwp_number": "4219", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: prueba", "last_modified_date": "2025-02-11T01:54:57.000+0000"}}, {"incident_id": "INC000000003653", "status": "Closed", "description": "Petición Hija relacionada a la afectación mayor 4289", "detailed_description": "Aplicativo Afectado: GroupCenterEmpresarial\nTipo de falla: Transacción en proceso\nRuta por la que ingresa: Herramientas de trabajo / Modulo multiples riesgos\nProducto o solución afectada: todo riesgo empresarial\nPóliza: 900001133056\nOperación: renovacion\nUsuario afectado: hermmeba\nContacto del usuario: 3102831798\nNombre de la oficina: Sucursal Cúcuta\nCotización: 03012518250102988150\n¿La póliza tiene oneroso?: No\nDescripción del error: Solicitud de renovacion y se queda en proceso\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "HPD_CI_ReconID: OI-02DCA5B8892011EF899A5E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: REINICIO\\nClosure Product Category Tier1: Aplicaciones Canal Asesor\\nResolution Category Tier 2: REINICIO\\nClosure Product Category Tier2: Group Center\\nHPD_CI: Group Center Empresariales\\nClosure Product Category Tier3: Empresarial\\nDWP_SRID: 4425\\nDWP_SRInstanceID: AGGH4ZM7BVS1QASQ6KTJSQ6KTJX0OW\\nIncident Number: INC000000003653\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: Aplicaciones Seguros\\nCategorization Tier 2: Group Center\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: Aplicaciones Canal Asesor\\nProduct Categorization Tier 3: Empresarial\\nProduct Categorization Tier 2: Group Center\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000483;'maripoda';", "last_modified_date": "2025-02-11T07:00:01.000+0000", "create_date": "2025-01-21T21:57:10.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "<PERSON><PERSON>p<PERSON>, se atendió con el INC000000003488 - Id 4374", "resolution_method": null, "dwp_number": "4425", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\n<PERSON><PERSON> dup<PERSON>, se atendió con el INC000000003488 - Id 4374\n\nCategoría de resolución:\nGESTION DE HARDWARE/SOFTWARE.REINICIO.REINICIO", "last_modified_date": "2025-02-06T05:13:52.000+0000"}}, {"incident_id": "INC000000003461", "status": "Closed", "description": "Plantilla General", "detailed_description": "Aplicativo o equipo afectado: \nDescripción del error o falla: \nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-03DE58A8892011EF8A885E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Incidente\\nHPD_CI: Visor Aplicaciones Terceros\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 4314\\nIncident Number: INC000000003461\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC (Pruebas)\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000076;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2025-01-17T16:04:27.000+0000", "priority": "Medium", "impact": "2-Significant/Large", "urgency": "3-Medium", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "PRUEBA", "resolution_method": null, "dwp_number": "4314", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Se resuelve para que no afecte el tablero de mayores en PDN.", "last_modified_date": "2025-01-20T21:50:36.000+0000"}}, {"incident_id": "INC000000003691", "status": "Cancelled", "description": "Plantilla General", "detailed_description": "Aplicativo o equipo afectado: \nDescripción del error o falla: \nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-031F7424892011EF89D95E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Group Center\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Empresarial\\nHPD_CI: Cotizador de Autos\\nClosure Product Category Tier3: Empresarial\\nDWP_SRID: 4571\\nIncident Number: INC000000003691\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: Cotizador\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: Group Center\\nProduct Categorization Tier 3: Empresarial\\nProduct Categorization Tier 2: Empresarial\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000483;'maripoda';", "last_modified_date": "2025-02-16T01:49:39.000+0000", "create_date": "2025-01-24T15:34:00.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "4571", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Buen Día,\n\n*Se cancela caso por solicitud del usuario.\n\nFeliz Día.", "last_modified_date": "2025-02-14T21:04:35.000+0000"}}, {"incident_id": "INC000000003740", "status": "Cancelled", "description": "Plantilla General", "detailed_description": "Aplicativo o equipo afectado: Prueba\nDescripción del error o falla: \nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-031F7424892011EF89D95E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nClosure Product Category Tier1: Group Center\\nClosure Product Category Tier2: Empresarial\\nHPD_CI: Cotizador de Autos\\nClosure Product Category Tier3: Empresarial\\nDWP_SRID: 4473\\nIncident Number: INC000000003740\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: Aplicaciones Seguros\\nCategorization Tier 2: Cotizador\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: Group Center\\nProduct Categorization Tier 3: Empresarial\\nProduct Categorization Tier 2: Empresarial\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000478;'maripoda';", "last_modified_date": "2025-04-22T20:49:28.000+0000", "create_date": "2025-01-24T15:28:29.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "4473", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: prueba", "last_modified_date": "2025-02-11T01:43:25.000+0000"}}, {"incident_id": "INC000000003692", "status": "Cancelled", "description": "Plantilla General - Asignación directa", "detailed_description": "Aplicativo o equipo afectado:  PRUEBA\nDescripción del error o falla: \nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-031F7424892011EF89D95E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nClosure Product Category Tier1: Apigee\\nClosure Product Category Tier2: Apigee\\nHPD_CI: Cotizador de Autos\\nClosure Product Category Tier3: Apigee\\nDWP_SRID: 4475\\nIncident Number: INC000000003692\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: Servicios de Tecnologia\\nCategorization Tier 2: Gestion ITSM\\nCategorization Tier 3: Hallazgo en Estabilización\\nProduct Categorization Tier 1: API Autonomia\\nProduct Categorization Tier 3: Api Autonomia\\nProduct Categorization Tier 2: Vida\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000478;'maripoda';", "last_modified_date": "2025-02-18T21:00:18.000+0000", "create_date": "2025-01-24T16:00:22.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "4475", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000003929", "status": "Closed", "description": "Plantilla General", "detailed_description": "Aplicativo o equipo afectado: PRUEBA\nDescripción del error o falla:  No puede ingresar\nRuta por la que ingresa: AVA - XXX\nOperación que estaba realizando: Creación \nNombre del afectado: <PERSON>\nUbicación (Sede y piso):  \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-01CCC004892011EF88A65E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: AVA - Asistente Virtual Asesor\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Autenticación\\nHPD_CI: AVA - Asistente Virtual Asesores\\nClosure Product Category Tier3: Autenticación\\nDWP_SRID: 4611\\nIncident Number: INC000000003929\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: AVA - Asistente Virtual Asesor\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: AVA - Asistente Virtual Asesor\\nProduct Categorization Tier 3: Autenticación\\nProduct Categorization Tier 2: Autenticación\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000478;'maripoda';", "last_modified_date": "2025-02-15T23:15:14.000+0000", "create_date": "2025-01-28T19:18:44.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Prueba", "resolution_method": null, "dwp_number": "4611", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nPrueba\n\nCategoría de resolución:\nGestión BMC.Pruebas PDN.Consola", "last_modified_date": "2025-01-28T19:22:39.000+0000"}}, {"incident_id": "INC000000003930", "status": "Cancelled", "description": "Plantilla General", "detailed_description": "Aplicativo o equipo afectado: cotizador empresarial\nDescripción del error o falla:  bloqueo de aplicativo\nRuta por la que ingresa: cotizador\nOperación que estaba realizando: Gestión de ingreso.\nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-031F7424892011EF89D95E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nClosure Product Category Tier1: Cotizador\\nClosure Product Category Tier2: Empresarial\\nHPD_CI: Cotizador de Autos\\nClosure Product Category Tier3: Empresarial\\nDWP_SRID: 4612\\nIncident Number: INC000000003930\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: Cotizador\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: Cotizador\\nProduct Categorization Tier 3: Empresarial\\nProduct Categorization Tier 2: Empresarial\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-18T21:01:24.000+0000", "create_date": "2025-01-28T19:28:11.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "4612", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000003959", "status": "Cancelled", "description": "Pago Express", "detailed_description": "Aplicativo Afectado: PagoExpress\nOperación: 123\nPóliza / Recibo: 1234\nUsuario afectado: Maripoda\nRuta por la que ingresa: Prueba\nDescripción del error: PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-040A1C7C892011EF8AB05E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Incidente\\nHPD_CI: Pago Express\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 4641\\nDWP_SRInstanceID: AGGH4ZM7BVS1QASQWTT3SQWTT3WCHO\\nIncident Number: INC000000003959\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC (Pruebas)\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000134;1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2025-01-30T16:31:36.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "4641", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: PRUEBA", "last_modified_date": "2025-01-30T16:33:16.000+0000"}}, {"incident_id": "INC000000003786", "status": "Cancelled", "description": "Plantilla General", "detailed_description": "Aplicativo o equipo afectado: \nDescripción del error o falla: \nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-01FAEBE6892011EF88CD5E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nClosure Product Category Tier1: Cotizador Hogar\\nClosure Product Category Tier2: Propiedad\\nHPD_CI: Cotizador Hogar\\nClosure Product Category Tier3: Hogares Sura\\nDWP_SRID: 4723\\nIncident Number: INC000000003786\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: Aplicaciones Seguros\\nCategorization Tier 2: Cotizador\\nCategorization Tier 3: Bloqueos-Lentitudes\\nProduct Categorization Tier 1: Cotizador Hogar\\nProduct Categorization Tier 3: Hogares Sura\\nProduct Categorization Tier 2: Propiedad\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000478;'maripoda';", "last_modified_date": "2025-02-18T21:01:25.000+0000", "create_date": "2025-01-29T14:52:23.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "4723", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000003788", "status": "Cancelled", "description": "Plantilla General - SAP X1235GTR", "detailed_description": "Aplicativo o equipo afectado: \nDescripción del error o falla: \nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-01FAEBE6892011EF88CD5E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Incidente\\nHPD_CI: Cotizador <PERSON>gar\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 4620\\nIncident Number: INC000000003788\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC (Pruebas)\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-04-01T05:00:00.000+0000", "create_date": "2025-01-29T15:03:05.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Hola señor usuario la solución......", "resolution_method": null, "dwp_number": "4620", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Motivo de suspensión: Falta de información del usuario\n\nFecha de reactivación: 12/2/2025 15:00:00\n\nNota de suspensión: Hola, en espera de que el usuairo comparta la información.", "last_modified_date": "2025-02-11T20:00:47.000+0000"}}, {"incident_id": "INC000000004032", "status": "Closed", "description": "Plantilla General", "detailed_description": "Aplicativo o equipo afectado: \nDescripción del error o falla: \nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-01FAEBE6892011EF88CD5E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: MEMORIA\\nClosure Product Category Tier1: Aplicaciones Transversales\\nResolution Category Tier 2: AUMENTO\\nClosure Product Category Tier2: Otros\\nHPD_CI: Cotizador Hogar\\nClosure Product Category Tier3: Otros\\nDWP_SRID: 4774\\nIncident Number: INC000000004032\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: Cotizador\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: Cotizador Hogar\\nProduct Categorization Tier 3: Hogares Sura\\nProduct Categorization Tier 2: Propiedad\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000076;1000000478;'maripoda';", "last_modified_date": "2025-02-18T07:00:01.000+0000", "create_date": "2025-02-03T19:15:50.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): Caso de prueba cambio IM\nSolución:\nCausa Raíz (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO):", "resolution_method": null, "dwp_number": "4774", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nCausa (Motivo, origen o razón de que suceda el incidente): Caso de prueba cambio IM\nSolución:\nCausa <PERSON>í<PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO):\n\nCategoría de resolución:\nAUMENTO DE CAPACIDAD DISPOSITIVOS.AUMENTO.MEMORIA", "last_modified_date": "2025-02-13T03:44:32.000+0000"}}, {"incident_id": "INC000000004035", "status": "Closed", "description": "Plantilla General", "detailed_description": "Aplicativo o equipo afectado: \nDescripción del error o falla: \nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-031F7424892011EF89D95E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: CPU\\nClosure Product Category Tier1: Aplicaciones Transversales\\nResolution Category Tier 2: AUMENTO\\nClosure Product Category Tier2: Otros\\nHPD_CI: Cotizador de Autos\\nClosure Product Category Tier3: Otros\\nDWP_SRID: 4775\\nIncident Number: INC000000004035\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: ARL\\nCategorization Tier 2: DIOGENES\\nCategorization Tier 3: Bloqueos-Lentitudes\\nProduct Categorization Tier 1: Cotizador\\nProduct Categorization Tier 3: Empresarial\\nProduct Categorization Tier 2: Empresarial\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000076;1000000478;'maripoda';", "last_modified_date": "2025-04-22T20:49:28.000+0000", "create_date": "2025-02-03T19:53:09.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente):prueba\nSolución:\nCausa <PERSON>íz (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO):", "resolution_method": null, "dwp_number": "4775", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nCausa (Motivo, origen o razón de que suceda el incidente):prueba\nSolución:\nCausa Raíz (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO):\n\nCategoría de resolución:\nAUMENTO DE CAPACIDAD DISPOSITIVOS.AUMENTO.CPU", "last_modified_date": "2025-02-18T04:43:30.000+0000"}}, {"incident_id": "INC000000004169", "status": "Closed", "description": "Reporte de fallla", "detailed_description": "Aplicacion / Modulo afectado: ConsolaEspecialista\nTipo de error: Indisponibilidad Herramienta\nNombre Usuario Afectado: <PERSON>\nUsuario afectado: Maripoda\nContacto usuario afectado: **********\nDescripción del error: <PERSON>uenas tardes, su ayuda por favor. En la consola de tickets un mismo usuario tiene diferentes marcas de \"Special Handling\" o no se muestra la marca, este tema es urgente y se debe gestionar con prioridad, adjunto evidencia.", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-03925F2A892011EF8A415E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: RETROALIMENTACION\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: ADMINISTRACION BMC\\nClosure Product Category Tier2: Solicitud\\nHPD_CI: Acceso unico de Solicitudes\\nClosure Product Category Tier3: Modificación\\nDWP_SRID: 4897\\nDWP_SRInstanceID: AGGH4ZM7BVS1QASRL2UGSRL2UGVYD4\\nIncident Number: INC000000004169\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: Servicios de Tecnologia\\nCategorization Tier 2: Gestion ITSM\\nCategorization Tier 3: Mensaje Error/Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Mensaje Error/Falla\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "**********;1000000478;'maripoda';", "last_modified_date": "2025-02-18T07:00:07.000+0000", "create_date": "2025-02-12T22:34:38.000+0000", "priority": "Medium", "impact": "3-Moderate/Limited", "urgency": "3-Medium", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Confirmo que este usuario, está en la lista que se mandó a corregir el 23 de enero sobre el campo special handling, adjunto hilo del correo, adicional confirmo que los incidentes anteriormente creados a este usuario no tienen efecto de retroactividad en este tratamiento especial.", "resolution_method": null, "dwp_number": "4897", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nConfirmo que este usuario, está en la lista que se mandó a corregir el 23 de enero sobre el campo special handling, adjunto hilo del correo, adicional confirmo que los incidentes anteriormente creados a este usuario no tienen efecto de retroactividad en este tratamiento especial.\n\nCategoría de resolución:\nSERVICIOS DE TECNOLOGIA.ADMINISTRACION BMC.RETROALIMENTACION", "last_modified_date": "2025-02-13T20:49:43.000+0000"}}, {"incident_id": "INC000000004170", "status": "Cancelled", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: OtrasEmpresarial\nAplicativo o equipo afectado: Correo electrónico\nRuta por la que ingresa: \nTelefonos de afectado: \nUbicación del afectado  (Sede y piso): \nNombre completo del afectado: <EMAIL>\nUsuario con el que ingresó: \nDescripción del error: PRUEBA - Hola, su ayuda con <NAME_EMAIL>, no me llegan los correos.\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nClosure Product Category Tier1: Apigee\\nClosure Product Category Tier2: Apigee\\nClosure Product Category Tier3: Apigee\\nDWP_SRID: 4979\\nDWP_SRInstanceID: AGGEM7T2FGTSWASRL7C1SRL7C1LMTR\\nIncident Number: INC000000004170\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: Servicios de Tecnologia\\nCategorization Tier 2: Gestion ITSM\\nCategorization Tier 3: Hallazgo en Estabilización\\nProduct Categorization Tier 1: API Autonomia\\nProduct Categorization Tier 3: Api Autonomia\\nProduct Categorization Tier 2: Vida\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-18T21:00:18.000+0000", "create_date": "2025-02-12T23:54:13.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "4979", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000004312", "status": "Cancelled", "description": "Petición Hija relacionada a la afectación mayor 4775", "detailed_description": "Aplicativo o equipo afectado: \nDescripción del error o falla: \nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "Associated Alarm: None\\nClosure Product Category Tier1: Cotizador\\nClosure Product Category Tier2: Empresarial\\nClosure Product Category Tier3: Empresarial\\nDWP_SRID: 5162\\nDWP_SRInstanceID: AGGEM7T2FGTSWASRNC21SRNC21QS8X\\nIncident Number: INC000000004312\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: Aplicaciones ARL\\nCategorization Tier 2: Diogenes\\nCategorization Tier 3: Bloqueos-Lentitudes\\nProduct Categorization Tier 1: Cotizador\\nProduct Categorization Tier 3: Empresarial\\nProduct Categorization Tier 2: Empresarial\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000478;'maripoda';", "last_modified_date": "2025-02-18T20:55:51.000+0000", "create_date": "2025-02-13T20:31:34.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "5162", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000004230", "status": "Cancelled", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: OtrasEmpresarial\nAplicativo o equipo afectado: Correo electrónico\nRuta por la que ingresa: \nTelefonos de afectado: \nUbicación del afectado  (Sede y piso): \nNombre completo del afectado: <EMAIL>\nUsuario con el que ingresó: \nDescripción del error: PRUEBA - Hola, su ayuda con <NAME_EMAIL>, no me llegan los correos.\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "Associated Alarm: None\\nClosure Product Category Tier1: Apigee\\nClosure Product Category Tier2: Apigee\\nClosure Product Category Tier3: Apigee\\nDWP_SRID: 4892\\nDWP_SRInstanceID: AGGH4ZM7BVS1QASRLJWSSRLJWSV62Q\\nIncident Number: INC000000004230\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: Servicios de Tecnologia\\nCategorization Tier 2: Gestion ITSM\\nCategorization Tier 3: Hallazgo en Estabilización\\nProduct Categorization Tier 1: API Autonomia\\nProduct Categorization Tier 3: Api Autonomia\\nProduct Categorization Tier 2: Vida\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-02-18T21:00:18.000+0000", "create_date": "2025-02-12T21:21:41.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "4892", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000004499", "status": "Cancelled", "description": "VaxThera", "detailed_description": "Affected's full name: <PERSON>\nAffected user: Maripoda\nContact number: 123456\nAffected's e-mail: <EMAIL>\nUser location: <PERSON>rueba\nAffected URL or Link: https://surasoporteti-dwp.onbmc.com/dwp/app/#/checkout\nAffected service/equipment: Pueba\nError Description: Prueba\nHow many people are affected?: 1 person\nDoes the report arise from a Salesforce PQRS?: Yes\nMeans of Income: Petition rights\nWhat is the PQRS number?: 12345\n", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-01CCC004892011EF88A65E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Incidente\\nHPD_CI: AVA - Asistente Virtual Asesores\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 5202\\nDWP_SRInstanceID: AGGG8BPUGH3W9ASRO249SRO249TJAU\\nIncident Number: INC000000004499\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: Servicios de Tecnologia\\nCategorization Tier 2: Gestion ITSM\\nCategorization Tier 3: Hallazgo en Estabilización\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000136;1000000478;'maripoda';", "last_modified_date": "2025-04-01T05:00:00.000+0000", "create_date": "2025-02-14T13:08:46.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Prueba resolución.", "resolution_method": null, "dwp_number": "5202", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nPrueba resolución.\n\nCategoría de resolución:\nGestión BMC.Pruebas PDN.Consola", "last_modified_date": "2025-02-14T17:01:27.000+0000"}}, {"incident_id": "INC000000004572", "status": "Cancelled", "description": "Plantilla General", "detailed_description": "Aplicativo o equipo afectado: \nDescripción del error o falla: \nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-01BF46CC892011EF889B5E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nClosure Product Category Tier1: Apigee\\nClosure Product Category Tier2: Apigee\\nHPD_CI: Saludweb\\nClosure Product Category Tier3: Apigee\\nDWP_SRID: 5347\\nIncident Number: INC000000004572\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: Servicios de Tecnologia\\nCategorization Tier 2: Gestion ITSM\\nCategorization Tier 3: Indisponibilidad Herramienta\\nProduct Categorization Tier 1: Analizador de Requerimientos\\nProduct Categorization Tier 3: Analizador de Requerimientos\\nProduct Categorization Tier 2: Analizador de Requerimientos\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000478;'maripoda';", "last_modified_date": "2025-02-18T20:58:47.000+0000", "create_date": "2025-02-17T21:54:04.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "5347", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000004713", "status": "Closed", "description": "Reporte de fallla", "detailed_description": "Aplicacion / Modulo afectado: ReportesDashboard\nTipo de error: Error en Reportes\nNombre Usuario Afectado: <PERSON>\nUsuario afectado: Maripoda\nContacto usuario afectado: **********\nDescripción del error: Buen día, su ayuda por favor revisando las sabanas Abierto, Completo y Cerrados que esta presentando duplicidad en los registros. Favor asignar a <PERSON>, gracias.", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-03925F2A892011EF8A415E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Incidente\\nHPD_CI: Acceso unico de Solicitudes\\nClosure Product Category Tier3: Error en Reportes\\nDWP_SRID: 5410\\nDWP_SRInstanceID: AGGF4Y8FJHH1YASRXMBBSRXMBBSSU1\\nIncident Number: INC000000004713\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en Reportes\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "**********;'maripoda';", "last_modified_date": "2025-02-25T07:00:07.000+0000", "create_date": "2025-02-19T13:22:58.000+0000", "priority": "Medium", "impact": "3-Moderate/Limited", "urgency": "3-Medium", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "La novedad de la duplicidad se debe a que, por alguna razón, están quedando dos registros con la misma fecha de registro inicial, donde se relaciona la plantilla. Sin embargo, uno de ellos aparece sin valor, mostrando solo corchetes, y en la descripción se indica \"data error\".\n\nHe configurado el reporte para que no muestre los registros con \"data error\", pero quedo atento en caso de que sea necesario aplicar otro filtro, ya que aún no comprendo la causa exacta de este error.", "resolution_method": null, "dwp_number": "5410", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nLa novedad de la duplicidad se debe a que, por alguna razón, están quedando dos registros con la misma fecha de registro inicial, donde se relaciona la plantilla. Sin embargo, uno de ellos aparece sin valor, mostrando solo corchetes, y en la descripción se indica \"data error\".\n\nHe configurado el reporte para que no muestre los registros con \"data error\", pero quedo atento en caso de que sea necesario aplicar otro filtro, ya que aún no comprendo la causa exacta de este error.\n\nCategoría de resolución:\nGestión BMC.Pruebas PDN.Consola", "last_modified_date": "2025-02-19T15:13:33.000+0000"}}, {"incident_id": "INC000000004818", "status": "Closed", "description": "PRUEBA - Plantilla General", "detailed_description": "Aplicativo o equipo afectado: \nDescripción del error o falla: \nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-CA5C9870EA0D11EF99755E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: REINICIO\\nClosure Product Category Tier1: Aplicaciones Transversales\\nResolution Category Tier 2: REINICIO\\nClosure Product Category Tier2: Somos Sura\\nHPD_CI: Cotizador Agro\\nClosure Product Category Tier3: Somos Sura\\nDWP_SRID: 5418\\nIncident Number: INC000000004818\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: EPS\\nCategorization Tier 2: Gestion Comercial Somos Sura\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: Aplicaciones Transversales\\nProduct Categorization Tier 3: Somos Sura\\nProduct Categorization Tier 2: Somos Sura\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000076;1000000478;'maripoda';", "last_modified_date": "2025-03-19T07:00:01.000+0000", "create_date": "2025-02-19T15:07:57.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente):\nSolución: Fin de prueba \nCausa Raíz (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO):", "resolution_method": null, "dwp_number": "5418", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Se solicita apoyo al puente para solucionar el presente IM de pruebas", "last_modified_date": "2025-03-13T21:36:44.000+0000"}}, {"incident_id": "INC000000004732", "status": "Closed", "description": "Plantilla General", "detailed_description": "Aplicativo o equipo afectado: \nDescripción del error o falla: \nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-02C161EA892011EF89835E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: APLICATIVOS\\nClosure Product Category Tier1: IPSA\\nResolution Category Tier 2: CAPACITACION\\nClosure Product Category Tier2: Administracion Solucion\\nHPD_CI: IPSA\\nClosure Product Category Tier3: Administracion Solucion\\nDWP_SRID: 5450\\nIncident Number: INC000000004732\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: EPS\\nCategorization Tier 2: IPSA\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: IPSA\\nProduct Categorization Tier 3: Remisiones\\nProduct Categorization Tier 2: Remisiones\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000478;1000000419;'maripoda';", "last_modified_date": "2025-03-02T07:00:10.000+0000", "create_date": "2025-02-20T20:38:08.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Incidencia de prueba.", "resolution_method": null, "dwp_number": "5450", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nIncidencia de prueba.\n\nCategoría de resolución:\nCAPACITACION.CAPACITACION.APLICATIVOS", "last_modified_date": "2025-02-24T14:37:20.000+0000"}}, {"incident_id": "INC000000004817", "status": "Closed", "description": "Reporte de fallla", "detailed_description": "Aplicacion / Modulo afectado: ReportesDashboard\nTipo de error: <PERSON><PERSON>r en Reportes\nNombre Usuario Afectado: <PERSON>\nUsuario afectado: Maripoda\nContacto usuario afectado: **********\nDescripción del error: Buen día, su ayuda por favor revisando la sabana \"semá<PERSON>ro new\" esta presentando novedades en el calculo de los hitos, los rangos de tiempo consumido no corresponde a los hitos que se están asignando. La prueba se realizó con la data a corte 18 Feb 3pm, se descargo el archivo, se identificaron los rangos y tiempos estimados, por ejemplo para la petición 4287, el caso fue abierto desde 20 enero (fecha posterior al ajuste del reporte) hasta ayer ya llevaba más de 20 días en estados de apertura, nunca ha sido suspendido y aparece hito >=50% cuando debería estar superior al 150%, muchas gracias por su ayuda. \n\nFavor asignar a <PERSON>, gracias.\n\nAdjunto archivo de pruebas", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-03925F2A892011EF8A415E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: REVISE MENSAJE DE ERROR\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: ADMINISTRACION BMC\\nClosure Product Category Tier2: Incidente\\nHPD_CI: Acceso unico de Solicitudes\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 5291\\nDWP_SRInstanceID: AGGG8BPUGH3W9ASRXOXUSRXOXUW7RQ\\nIncident Number: INC000000004817\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "**********;'maripoda';", "last_modified_date": "2025-03-06T07:00:03.000+0000", "create_date": "2025-02-19T15:04:46.000+0000", "priority": "Medium", "impact": "3-Moderate/Limited", "urgency": "3-Medium", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Se realiza ajuste en el reporte \"Sabana de Semaforo New\", ya muestra correctamente los datos en la columna \"% Avance\". Adjunto evidencia", "resolution_method": null, "dwp_number": "5291", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nSe realiza ajuste en el reporte \"Sabana de Semaforo New\", ya muestra correctamente los datos en la columna \"% Avance\". Adjunto evidencia\n\nCategoría de resolución:\nSERVICIOS DE TECNOLOGIA.ADMINISTRACION BMC.REVISE MENSAJE DE ERROR", "last_modified_date": "2025-02-28T14:29:50.000+0000"}}, {"incident_id": "INC000000004881", "status": "Closed", "description": "VaxThera", "detailed_description": "Affected's full name: <PERSON>\nAffected user: Maripoda\nContact number: 12345678\nAffected's e-mail: <EMAIL>\nUser location: <PERSON>rueba\nAffected URL or Link: https://surasoporteti-dwp.onbmc.com/dwp/app/#/checkout\nAffected service/equipment: Pueba\nError Description: PRUEBA - MAYOR\nHow many people are affected?: 1 person\nDoes the report arise from a Salesforce PQRS?: Not\n", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-026B5B10892011EF89355E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: SomosSura\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 5572\\nDWP_SRInstanceID: AGGG8BPUGH3W9ASSB23GSSB23GOKMV\\nIncident Number: INC000000004881\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000136;1000000478;'maripoda';", "last_modified_date": "2025-03-19T07:00:03.000+0000", "create_date": "2025-02-21T13:35:04.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Prueba", "resolution_method": null, "dwp_number": "5572", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nPrueba\n\nCategoría de resolución:\nGestión BMC.Pruebas PDN.Consola", "last_modified_date": "2025-03-13T14:20:40.000+0000"}}, {"incident_id": "INC000000005290", "status": "Closed", "description": "Reporte de fallla", "detailed_description": "Aplicacion / Modulo afectado: ConsolaEspecialista\nTipo de error: <PERSON><PERSON><PERSON>/Falla\nNombre Usuario Afectado: <PERSON>\nUsuario afectado: Maripoda\nContacto usuario afectado: **********\nDescripción del error: Buen día, su ayuda revisando la siguiente novedad. Se están creando WO a nombre de los compañeros sin que nadie los active  o cree, esto esta generando confusión en los equipos y aumentando la volumetria, además estan quedando desatendidas porque hoy ese proceso no esta implementado en la operación, muchas gracias.", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-03925F2A892011EF8A415E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: SOLUCIONADO SIN ACCIONES\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: EXCLUSIVO GESTION DE INCIDENTES\\nClosure Product Category Tier2: Incidente\\nHPD_CI: Acceso unico de Solicitudes\\nClosure Product Category Tier3: Hallazgo en Estabilización\\nDWP_SRID: 6056\\nDWP_SRInstanceID: AGGF4Y8FJHH1YASSGW2SSSGW2STZ72\\nIncident Number: INC000000005290\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Plataforma ITSM BMC\\nProduct Categorization Tier 2: Plataforma ITSM BMC\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "**********;'maripoda';", "last_modified_date": "2025-04-22T20:49:28.000+0000", "create_date": "2025-02-24T13:38:20.000+0000", "priority": "Medium", "impact": "3-Moderate/Limited", "urgency": "3-Medium", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Buenas tardes, de acuerdo al análisis y verificación con el equipo de implementación de forense, me confirman que cuando se genera el forense se disparan las notificaciones al grupo gestor y al grupo asignado de esta petición, por eso llega esta notificación a todos los integrantes de este grupo > Se ha asignado a su grupo como grupo de usuarios asignados para la orden de trabajo WO0000000000531. Compruebe la petición.", "resolution_method": null, "dwp_number": "6056", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nBuenas tardes, de acuerdo al análisis y verificación con el equipo de implementación de forense, me confirman que cuando se genera el forense se disparan las notificaciones al grupo gestor y al grupo asignado de esta petición, por eso llega esta notificación a todos los integrantes de este grupo > Se ha asignado a su grupo como grupo de usuarios asignados para la orden de trabajo WO0000000000531. Compruebe la petición.\n\nCategoría de resolución:\nPROCESO DE INCIDENTES.EXCLUSIVO GESTION DE INCIDENTES.S<PERSON><PERSON><PERSON>AD<PERSON> SIN ACCIONES", "last_modified_date": "2025-03-15T22:55:40.000+0000"}}, {"incident_id": "INC000000005801", "status": "Cancelled", "description": "prueba ANS", "detailed_description": "Aplicativo o equipo afectado: \nDescripción del error o falla: \nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 6576\\nIncident Number: INC000000005801\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000478;'maripoda';", "last_modified_date": "2025-06-17T16:55:28.000+0000", "create_date": "2025-02-25T14:52:05.000+0000", "priority": "Medium", "impact": "3-Moderate/Limited", "urgency": "3-Medium", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "6576", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Prueba", "last_modified_date": "2025-06-17T16:55:29.000+0000"}}, {"incident_id": "INC000000009102", "status": "Cancelled", "description": "Mesa Express - Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: \nAplicativo o equipo afectado: OtrosPRUEBA\nRuta por la que ingresa: PRUEBA\nTelefonos de afectado: 123456789\nUbicación del afectado  (Sede y piso): PRUEBA\nNombre completo del afectado: PRUEBA\nUsuario con el que ingresó: PRUEBA\nDescripción del error: PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 9329\\nDWP_SRInstanceID: AGGG8BPUGH3W9ASSLYGWSSLYGW0H4Y\\nIncident Number: INC000000009102\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000124;1000000478;'maripoda';", "last_modified_date": "2025-06-17T16:52:32.000+0000", "create_date": "2025-03-04T16:40:11.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): .", "resolution_method": null, "dwp_number": "9329", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Prueba", "last_modified_date": "2025-06-17T16:52:31.000+0000"}}, {"incident_id": "INC000000009173", "status": "Closed", "description": "Reporte de fallla", "detailed_description": "Aplicacion / Modulo afectado: ReportesDashboard\nTipo de error: Error en Reportes\nNombre Usuario Afectado: Mariana Restrepo Florez\nUsuario afectado: Marirsfl\nContacto usuario afectado: **********\nDescripción del error: <PERSON><PERSON>, su ayuda revisando el reporte “Sabana de Incidentes cerrados” en el cual se identifica un error en los campos de OLAS, a partir de los incidentes cerrados desde el 3 de febrero. Ya que aparece como N/A, lo que haría referencia a que esa OLA no aplico en el caso, sin embargo, en el momento de revisar la consola, estas OLAS si aplicaron.\n\nAdjunto evidencia del reporte descargado y de algunos casos revisados en la consola que si aplica el OLA.", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: REVISE MENSAJE DE ERROR\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: ADMINISTRACION BMC\\nClosure Product Category Tier2: Incidente\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Error en Reportes\\nDWP_SRID: 9574\\nDWP_SRInstanceID: AGGG8BPUGH3W9ASSM01LSSM01L6IZS\\nIncident Number: INC000000009173\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en Reportes\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "**********;'maripoda';", "last_modified_date": "2025-04-22T20:49:28.000+0000", "create_date": "2025-03-04T21:06:29.000+0000", "priority": "Medium", "impact": "3-Moderate/Limited", "urgency": "3-Medium", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "\"08 Abril: Ya se encuentra certificado por ANS Sura\n21 Mar: Se ajusta el reporte, pendiente de certificar en PDN.\n\n13 Mar: Se realizó entrega del reporte a Mariana, Nelson realizó algunas sugerencias.\n\n12 Mar: Con Nelson esta realizando los ajustes en producción, se tiene un espacio citado para jueves 13 830 con Mariana para realizar pruebas.\n\n7 Mar: Pendiente entendimiento con Nelson hora propuesta 11:30am.\n\n10 Marzo <PERSON>: <PERSON><PERSON><PERSON>da nos indica que el ajuste ya esta en QA para hacer pruebas, se programa espacio para el día 11 MArzo con Gestion ANS para certificar\"", "resolution_method": null, "dwp_number": "9574", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\n\"08 Abril: Ya se encuentra certificado por ANS Sura\n21 Mar: Se ajusta el reporte, pendiente de certificar en PDN.\n\n13 Mar: Se realizó entrega del reporte a Mariana, <PERSON> realizó algunas sugerencias.\n\n12 Mar: Con <PERSON> esta realizando los ajustes en producción, se tiene un espacio citado para jueves 13 830 con Mariana para realizar pruebas.\n\n7 Mar: Pendiente entendimiento con Nelson hora propuesta 11:30am.\n\n10 Marzo Sura: Equipo Sonda nos indica que el ajuste ya esta en QA para hacer pruebas, se programa espacio para el día 11 MArzo con Gestion ANS para certificar\"\n\nCategoría de resolución:\nSERVICIOS DE TECNOLOGIA.ADMINISTRACION BMC.REVISE MENSAJE DE ERROR", "last_modified_date": "2025-04-09T03:09:35.000+0000"}}, {"incident_id": "INC000000013617", "status": "Closed", "description": "Reporte de fallla", "detailed_description": "Aplicacion / Modulo afectado: ConsolaEspecialista\nTipo de error: Hallazgo en Estabilización\nNombre Usuario Afectado: <PERSON>\nUsuario afectado: Maripoda\nContacto usuario afectado: **********\nDescripción del error: Su ayuda por favor, al crear los incidentes desde la consola no esta arrojando ID de petición, comparto casos de ejemplo, por favor realizar el análisis para entender porque se presenta la novedad y garantizar que no vuelva a ocurrir.\nINC000000013469\nINC000000013315\nINC000000013180", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: ESCALAMIENTO A FABRICA\\nClosure Product Category Tier1: Fabrica de Soluciones\\nResolution Category Tier 2: ADMINISTRACION BMC\\nClosure Product Category Tier2: Fabrica de Soluciones\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Fabrica de Soluciones\\nDWP_SRID: 16179\\nDWP_SRInstanceID: AGGJYA2JFNGSXASTATPSSTATPSWLKX\\nIncident Number: INC000000013617\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Plataforma ITSM BMC\\nProduct Categorization Tier 2: Plataforma ITSM BMC\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "**********;'maripoda';", "last_modified_date": "2025-05-11T07:00:01.000+0000", "create_date": "2025-03-12T19:16:44.000+0000", "priority": "Medium", "impact": "3-Moderate/Limited", "urgency": "3-Medium", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "30 abril Sonda: Avance fabrica Caso fabrica 01971014\n\nLamentablemente me informan desde el equipo de soporte BMC que el problema de uso de caracteres espaciales en el campo mencionado se corrige en la versión 25.2: \n\nEl problema es causado por el defecto DRD21-117923 y está solucionado en la versión 25.2.", "resolution_method": null, "dwp_number": "16179", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\n30 abril Sonda: Avance fabrica Caso fabrica 01971014\n\nLamentablemente me informan desde el equipo de soporte BMC que el problema de uso de caracteres espaciales en el campo mencionado se corrige en la versión 25.2: \n\nEl problema es causado por el defecto DRD21-117923 y está solucionado en la versión 25.2.\n\nCategoría de resolución:\nSERVICIOS DE TECNOLOGIA.ADMINISTRACION BMC.ESCALAMIENTO A FABRICA", "last_modified_date": "2025-05-05T13:42:12.000+0000"}}, {"incident_id": "INC000000013925", "status": "Cancelled", "description": "Plantilla General", "detailed_description": "Prueba Reapertura", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 16862\\nIncident Number: INC000000013925\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000478;'maripoda';", "last_modified_date": "2025-06-17T16:51:51.000+0000", "create_date": "2025-03-13T14:25:28.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Prueba", "resolution_method": null, "dwp_number": "16862", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Prueba", "last_modified_date": "2025-06-17T16:51:51.000+0000"}}, {"incident_id": "INC000000014092", "status": "Cancelled", "description": "Comfama", "detailed_description": "Nombre completo del afectado: <PERSON>\nUsuario de red: Maripoda\nTeléfono del afectado: **********\nCorreo del afectado: <EMAIL>\nUbicación del afectado: Prueba\nRuta por la que ingresa: Prueba\nTipo y número de identificación del afectado: \nProducto / Proceso: Ipsa- Consulta MD (Especialista - Otras Consultas)\nNombre del paciente: PRUEBA\nTipo y número de identificación del paciente: C1\nAutorización/número de orden/ Factura/Radicado: 1\nDescripción del error: Prueba\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-02C161EA892011EF89835E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nClosure Product Category Tier1: IPSA\\nClosure Product Category Tier2: Remisiones\\nHPD_CI: IPSA\\nClosure Product Category Tier3: Remisiones\\nDWP_SRID: 17109\\nDWP_SRInstanceID: AGGJYA2JFNGSXASTCKAJSTCKAJV7CW\\nIncident Number: INC000000014092\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: EPS\\nCategorization Tier 2: IPSA\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: IPSA\\nProduct Categorization Tier 3: Remisiones\\nProduct Categorization Tier 2: Remisiones\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000541;'maripoda';", "last_modified_date": "2025-03-13T18:34:31.000+0000", "create_date": "2025-03-13T15:57:26.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "17109", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Se cancela caso ya que era de prueba.", "last_modified_date": "2025-03-13T18:34:30.000+0000"}}, {"incident_id": "INC000000014096", "status": "Cancelled", "description": "Comfama", "detailed_description": "Nombre completo del afectado: <PERSON>\nUsuario de red: Maripoda\nTeléfono del afectado: **********\nCorreo del afectado: <EMAIL>\nUbicación del afectado: Prueba\nRuta por la que ingresa: Prueba\nTipo y número de identificación del afectado: \nProducto / Proceso: Ipsa- Consulta MD (Especialista - Otras Consultas)\nNombre del paciente: PRUEBA\nTipo y número de identificación del paciente: C1\nAutorización/número de orden/ Factura/Radicado: 1\nDescripción del error: Prueba\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-02C161EA892011EF89835E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nClosure Product Category Tier1: IPSA\\nClosure Product Category Tier2: Administracion Solucion\\nHPD_CI: IPSA\\nClosure Product Category Tier3: Administracion Solucion\\nDWP_SRID: 17122\\nDWP_SRInstanceID: AGGJYA2JFNGSXASTCK9JSTCK9JVYMB\\nIncident Number: INC000000014096\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: EPS\\nCategorization Tier 2: IPSA\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: IPSA\\nProduct Categorization Tier 3: Administracion Solucion\\nProduct Categorization Tier 2: Administracion Solucion\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000419;'maripoda';", "last_modified_date": "2025-03-13T16:32:07.000+0000", "create_date": "2025-03-13T16:02:01.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "17122", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Buenos dias \n\nse cancela caso ya que era una prueba de la plantilla de Comfama", "last_modified_date": "2025-03-13T16:32:06.000+0000"}}, {"incident_id": "INC000000015276", "status": "Cancelled", "description": "Plantilla General", "detailed_description": "Aplicativo o equipo afectado: \nDescripción del error o falla: \nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nIncident Number: INC000000015276\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000478;'maripoda';", "last_modified_date": "2025-03-17T16:41:53.000+0000", "create_date": "2025-03-17T13:47:05.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Prueba notificación usuario caso sin ID de petición", "resolution_method": null, "dwp_number": "INC000000015276", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Prueba inc relacionados", "last_modified_date": "2025-03-17T16:41:53.000+0000"}}, {"incident_id": "INC000000015309", "status": "Cancelled", "description": "INCIDENTE DE PRUEBAS", "detailed_description": "Aplicativo o equipo afectado: \nDescripción del error o falla: INCIDENTE DE PRUEBAS\nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nDirect Contact City: MEDELLIN\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nIncident Number: INC000000015309\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000124;1000000478;'maripoda';", "last_modified_date": "2025-04-22T20:49:28.000+0000", "create_date": "2025-03-17T13:45:31.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "INC000000015309", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: pruebas no atender", "last_modified_date": "2025-03-17T14:17:55.000+0000"}}, {"incident_id": "INC000000015311", "status": "Cancelled", "description": "INCIDENTE DE PRUEBAS", "detailed_description": "Aplicativo o equipo afectado: \nDescripción del error o falla:  INCIDENTE DE PRUEBAS\nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nDirect Contact City: MEDELLIN\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nIncident Number: INC000000015311\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000124;1000000478;'maripoda';", "last_modified_date": "2025-04-22T20:49:28.000+0000", "create_date": "2025-03-17T13:47:41.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "INC000000015311", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: pruebas no atender", "last_modified_date": "2025-03-17T14:19:48.000+0000"}}, {"incident_id": "INC000000015518", "status": "Cancelled", "description": "Prueba", "detailed_description": "Prueba ID petición sin plantilla de incidencia", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 19124\\nIncident Number: INC000000015518\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000478;'maripoda';", "last_modified_date": "2025-06-17T16:51:06.000+0000", "create_date": "2025-03-17T16:22:21.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "19124", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Prueba", "last_modified_date": "2025-06-17T16:51:05.000+0000"}}, {"incident_id": "INC000000016240", "status": "Cancelled", "description": "Plantilla General", "detailed_description": "Prueba\nAplicativo o equipo afectado: \nDescripción del error o falla: \nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 20255\\nIncident Number: INC000000016240\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000478;'maripoda';", "last_modified_date": "2025-06-17T16:46:19.000+0000", "create_date": "2025-03-18T16:13:40.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "20255", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Prueba", "last_modified_date": "2025-06-17T16:46:19.000+0000"}}, {"incident_id": "INC000000016368", "status": "Cancelled", "description": "edge.exception.ExecuteRoutineException: \"Error tarifando la cobertura de \"Responsabilidad Civil\"", "detailed_description": "Aplicativo o equipo afectado: prueba no atender\nDescripción del error o falla: \nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nDirect Contact City: MEDELLIN\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nIncident Number: INC000000016368\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000076;1000000478;'maripoda';", "last_modified_date": "2025-04-22T20:49:28.000+0000", "create_date": "2025-03-18T21:39:05.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "INC000000016368", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: pruebas no atender", "last_modified_date": "2025-03-18T21:40:17.000+0000"}}, {"incident_id": "INC000000019074", "status": "Cancelled", "description": "PRUEBA EJEMPLO INCIDENTE", "detailed_description": "Aplicativo o equipo afectado: \nDescripción del error o falla: \nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 24433\\nIncident Number: INC000000019074\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000545;1000000478;'maripoda';", "last_modified_date": "2025-06-17T16:43:02.000+0000", "create_date": "2025-03-25T19:40:17.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "24433", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Prueba", "last_modified_date": "2025-06-17T16:43:02.000+0000"}}, {"incident_id": "INC000000019233", "status": "Closed", "description": "Plantilla General", "detailed_description": "Prueba Inc Mayor - <PERSON>", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 24601\\nIncident Number: INC000000019233\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000522;1000000478;'maripoda';", "last_modified_date": "2025-04-22T20:49:28.000+0000", "create_date": "2025-03-25T21:23:46.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente):\nSolución:\nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO):prueba", "resolution_method": null, "dwp_number": "24601", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nCausa (Motivo, origen o razón de que suceda el incidente):\nSolución:\nCausa Raíz (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO):prueba\n\nCategoría de resolución:\nGestión BMC.Pruebas PDN.Consola", "last_modified_date": "2025-03-26T05:38:42.000+0000"}}, {"incident_id": "INC000000019157", "status": "Cancelled", "description": "Sitios Web", "detailed_description": "Aplicativo Afectado: SitiosWeb\nNombre completo del afectado: <PERSON>uario de red: Maripoda\nTeléfono del afectado: **********\nCorreo del afectado: <EMAIL>\nUbicación del afectado: Prueba\nRuta por la que ingresa: Prueba\nAplicativo afectado: ARL Sura\n¿Se autenticó en el sitio?: No\nPerfil: \nOperación: \nURL: \nNavegador y versión: \nDescripción del error: Prueba creación\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-037266C0892011EF8A235E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: LLAMADA\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Cancelada por Usuario\\nHPD_CI: Permisos Temporales\\nClosure Product Category Tier3: Cancelada por Usuario\\nDWP_SRID: 24713\\nDWP_SRInstanceID: AGGJYA2JFNGSXASTP0I5STP0I5IBZB\\nIncident Number: INC000000019157\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000518;'maripoda';", "last_modified_date": "2025-05-06T03:52:45.000+0000", "create_date": "2025-03-25T22:25:27.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "24713", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Se cancela a solicitud del usuario pues es una prueba.", "last_modified_date": "2025-03-28T21:13:03.000+0000"}}, {"incident_id": "INC000000021979", "status": "Closed", "description": "Reporte de fallla", "detailed_description": "Aplicacion / Modulo afectado: ConsolaEspecialista\nTipo de error: Error de ingreso usuario\nNombre Usuario Afectado: <PERSON><PERSON>uario afectado: braygilo\nContacto usuario afectado: **********\nDescripción del error: <PERSON><PERSON> día, su ayuda por favor revisando el caso del compañero <PERSON><PERSON>, no le permite visualizar los incidentes que tiene asignados a él, muchas gracias. \n*Se realiza descartes del perfil y se envia indicaciones para buscar desde la consola de especialista sin exito.\n\nComparto evidencia.", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Incidente\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Mensaje Error/Falla\\nDWP_SRID: 28302\\nDWP_SRInstanceID: AGGEFKK2DFA3IASUASFMSUASFMY9BJ\\nIncident Number: INC000000021979\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Plataforma ITSM BMC\\nProduct Categorization Tier 2: Plataforma ITSM BMC\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "**********;'maripoda';", "last_modified_date": "2025-04-22T20:49:28.000+0000", "create_date": "2025-04-01T03:49:40.000+0000", "priority": "Medium", "impact": "3-Moderate/Limited", "urgency": "3-Medium", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Buenas noches, por favor validar de nuevo con el usuario braygilo, ya que se hicieron replicas y repisado y veo que si tiene estos privilegios", "resolution_method": null, "dwp_number": "28302", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nBuenas noches, por favor validar de nuevo con el usuario braygilo, ya que se hicieron replicas y repisado y veo que si tiene estos privilegios\n\nCategoría de resolución:\nGestión BMC.Pruebas PDN.Consola", "last_modified_date": "2025-04-05T01:56:51.000+0000"}}, {"incident_id": "INC000000023893", "status": "Closed", "description": "Reporte de fallla", "detailed_description": "Aplicacion / Modulo afectado: ConsolaEspecialista\nTipo de error: Otro\nNombre Usuario Afectado: <PERSON> afectado: andrtosn\nContacto usuario afectado: **********\nDescripción del error: <PERSON><PERSON>, su ayuda por favor revisando el error que se presenta en el momento de crear incidentes para ciertos usuarios, aparece el mensaje \"La información de la incidencia no es válida, use los menus de los campos Zona, Grupo del Sitio y Sitio, o la función de devolución con escritura anticipada del campo Sitio, para seleccionar esta información (1291039)\", gracias.", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: REVISE MENSAJE DE ERROR\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: ADMINISTRACION BMC\\nClosure Product Category Tier2: Incidente\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Mensaje Error/Falla\\nDWP_SRID: 30849\\nDWP_SRInstanceID: AGGEFKK2DFA3IASUFZKASUFZKABL2R\\nIncident Number: INC000000023893\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Plataforma ITSM BMC\\nProduct Categorization Tier 2: Plataforma ITSM BMC\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "**********;'maripoda';", "last_modified_date": "2025-04-22T20:49:28.000+0000", "create_date": "2025-04-03T22:56:43.000+0000", "priority": "Medium", "impact": "3-Moderate/Limited", "urgency": "3-Medium", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Buenos días, espero que estén muy bien \nSe realizó el barrido de las 1.030 ubicaciones/sitios que se encuentran creados en pdn y se garantiza que ya ninguna queda con espacio al final.\n\nSe adjunta los dos archivos con este consolidado y en la columna B se aprecia la formula con la que se hizo este barrido.\n\nRegistro 19 Error crítico > Reportado por @<PERSON>\n\"Cuando van a crear un incidente les sale el mensaje de error: La información de la incidencia no es válida\n\nNo es posible registrar incidente a nombre del usuario resaltado, genera mensaje de ubicación, este mismo mensaje nos registro con un usuario el fin de semana donde no se logro crear el caso por BMC\"\n\nIncidente DWP 30849 - INC000000023893 > Resuelta", "resolution_method": null, "dwp_number": "30849", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nBuenos días, espero que estén muy bien \nSe realizó el barrido de las 1.030 ubicaciones/sitios que se encuentran creados en pdn y se garantiza que ya ninguna queda con espacio al final.\n\nSe adjunta los dos archivos con este consolidado y en la columna B se aprecia la formula con la que se hizo este barrido.\n\nRegistro 19 Error crítico > Reportado por @<PERSON>\n\"Cuando van a crear un incidente les sale el mensaje de error: La información de la incidencia no es válida\n\nNo es posible registrar incidente a nombre del usuario resaltado, genera mensaje de ubicación, este mismo mensaje nos registro con un usuario el fin de semana donde no se logro crear el caso por BMC\"\n\nIncidente DWP 30849 - INC000000023893 > Resuelta\n\nCategoría de resolución:\nSERVICIOS DE TECNOLOGIA.ADMINISTRACION BMC.REVISE MENSAJE DE ERROR", "last_modified_date": "2025-04-14T14:37:57.000+0000"}}, {"incident_id": "INC000000025059", "status": "Cancelled", "description": "Plantilla General - Prueba", "detailed_description": "Aplicativo o equipo afectado: \nDescripción del error o falla: \nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 32170\\nIncident Number: INC000000025059\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000478;'maripoda';", "last_modified_date": "2025-04-07T15:45:06.000+0000", "create_date": "2025-04-07T15:41:28.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "32170", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Prueba", "last_modified_date": "2025-04-07T15:45:05.000+0000"}}, {"incident_id": "INC000000026657", "status": "Closed", "description": "Agenda Web", "detailed_description": "Nombre completo del afectado: <PERSON>\nUsuario de red: Maripoda\nTeléfono del afectado: **********\nCorreo del afectado: <EMAIL>\nUbicación del afectado (sede y piso): Prueba\nRuta por la que ingresa: Prueba\nProceso afectado: Causas de cancelación y bloqueos\nDescripción del error: <PERSON><PERSON><PERSON>, favor omitir.\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:CDROMDrive\\nHPD_CI_ReconID: OI-2F356E54EC2311EF9C993ACEF3832554\\nHPD_CI_FormName: BMC_CDROMDRIVE\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Old_Pruebasoas\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 34126\\nDWP_SRInstanceID: AGGJYA2JFNGSXASU6M4TSU6M4TXVCI\\nIncident Number: INC000000026657\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000125;1000000478;'maripoda';", "last_modified_date": "2025-04-16T07:01:03.000+0000", "create_date": "2025-04-09T16:44:13.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Prueba External System Ticket", "resolution_method": null, "dwp_number": "34126", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nPrueba External System Ticket\n\nCategoría de resolución:\nGestión BMC.Pruebas PDN.Consola", "last_modified_date": "2025-04-10T19:24:19.000+0000"}}, {"incident_id": "INC000000027826", "status": "Closed", "description": "Cotizador- Empresarial", "detailed_description": "Aplicativo Afectado: CotizadorEmpresarial\nTipo de falla: \nProducto / Proceso: Nuevo\nProducto: Agentes de Carga\nUsuario afectado: Maripoda\nCelular del afectado: 1567890\nRuta por la que ingresa: Prueba - Prioridad Baja\nCotización: 1234566\nPoliza: \nRadicado y/o poliza: 1234567976400865\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: No\n¿La póliza tiene oneroso?: No\nDescripción del error: Detalle de la afectación\n¿Cuántas personas hay afectadas?: 1 persona¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 35699\\nDWP_SRInstanceID: AGGEFKK2DFA3IASUKFCRSUKFCRDH9Z\\nIncident Number: INC000000027826\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-04-17T07:02:49.000+0000", "create_date": "2025-04-11T14:24:00.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): .\nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO):", "resolution_method": null, "dwp_number": "35699", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nCausa (Motivo, origen o razón de que suceda el incidente): .\nSolución: \nCausa Raíz (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO):\n\nCategoría de resolución:\nGestión BMC.Pruebas PDN.Consola", "last_modified_date": "2025-04-11T14:42:28.000+0000"}}, {"incident_id": "INC000000028056", "status": "Cancelled", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: \nAplicativo o equipo afectado: OtrosPRUEBA\nRuta por la que ingresa: Prueba\nTelefonos de afectado: 123456\nUbicación del afectado  (Sede y piso): Prueba\nNombre completo del afectado: <PERSON> con el que ingresó: Maripoda\nDescripción del error: Prueba SH\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-026B5B10892011EF89355E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: SomosSura\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 36214\\nDWP_SRInstanceID: AGGEFKK2DFA3IASUK9ZMSUK9ZM61ED\\nIncident Number: INC000000028056\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000125;1000000478;'maripoda';", "last_modified_date": "2025-05-08T16:05:15.000+0000", "create_date": "2025-04-11T19:41:02.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "36214", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000029296", "status": "Closed", "description": "Plantilla General", "detailed_description": "Prueba suspensión", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 37495\\nIncident Number: INC000000029296\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000478;'maripoda';", "last_modified_date": "2025-05-12T07:00:05.000+0000", "create_date": "2025-04-15T11:54:16.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Prueba", "resolution_method": null, "dwp_number": "37495", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nPrueba\n\nCategoría de resolución:\nGestión BMC.Pruebas PDN.Consola", "last_modified_date": "2025-05-06T19:21:42.000+0000"}}, {"incident_id": "INC000000030268", "status": "Closed", "description": "Plantilla General", "detailed_description": "Prueba ANS - Semaforo", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-02C161EA892011EF89835E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: IPSA\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 38623\\nIncident Number: INC000000030268\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000478;'maripoda';", "last_modified_date": "2025-04-30T07:00:18.000+0000", "create_date": "2025-04-16T16:28:49.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Prueba cierre masivo", "resolution_method": null, "dwp_number": "38623", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nPrueba cierre masivo\n\nCategoría de resolución:\nGestión BMC.Pruebas PDN.Consola", "last_modified_date": "2025-04-24T17:34:56.000+0000"}}, {"incident_id": "INC000000030195", "status": "Closed", "description": "Reporte de fallla", "detailed_description": "Aplicacion / Modulo afectado: PortalServiciosTecnologia\nTipo de error: Men<PERSON><PERSON>/Falla\nNombre Usuario Afectado: <PERSON>\nUsuario afectado: Maripoda\nContacto usuario afectado: **********\nDescripción del error: Buen día, favor revisar el flujo de PQRS al enviar solicitudes estan creandonse en \"Fallo\" adjunto error. Para garantizar que las solicitudes que estén en \"Fallo\" no queden desatendidas, su ayuda identificando todos los casos que hasta la fecha estan en este estado para darle tratamiento con el equipo responsable, muchas gracias por su ayuda.", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: REVISE MENSAJE DE ERROR\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: ADMINISTRACION BMC\\nClosure Product Category Tier2: Incidente\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Mensaje Error/Falla\\nDWP_SRID: 38451\\nDWP_SRInstanceID: AGGJYA2JFNGSXASUT4WKSUT4WKQ0YK\\nIncident Number: INC000000030195\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Plataforma ITSM BMC\\nProduct Categorization Tier 2: Plataforma ITSM BMC\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "**********;'maripoda';", "last_modified_date": "2025-05-04T07:00:07.000+0000", "create_date": "2025-04-16T14:33:04.000+0000", "priority": "Medium", "impact": "3-Moderate/Limited", "urgency": "3-Medium", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "<PERSON>uenas noches, se realizó ajuste de manera correcta en el flujo del servicio Crea PQR, por favor validar", "resolution_method": null, "dwp_number": "38451", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nBuenas noches, se realizó ajuste de manera correcta en el flujo del servicio Crea PQR, por favor validar\n\nCategoría de resolución:\nSERVICIOS DE TECNOLOGIA.ADMINISTRACION BMC.REVISE MENSAJE DE ERROR", "last_modified_date": "2025-04-29T02:21:55.000+0000"}}, {"incident_id": "INC000000032662", "status": "Closed", "description": "Plantilla General", "detailed_description": "Aplicativo o equipo afectado: \nDescripción del error o falla: \nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-027F8D10892011EF894A5E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nDirect Contact City: MEDELLIN\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Policy Center\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 41830\\nIncident Number: INC000000032662\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000126;'maripoda';", "last_modified_date": "2025-04-29T07:02:32.000+0000", "create_date": "2025-04-23T17:15:09.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Se cierra incidente por pruebas en la herramienta", "resolution_method": null, "dwp_number": "41830", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nSe cierra incidente por pruebas en la herramienta\n\nCategoría de resolución:\nGestión BMC.Pruebas PDN.Consola", "last_modified_date": "2025-04-23T19:06:26.000+0000"}}, {"incident_id": "INC000000033606", "status": "Cancelled", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: \nAplicativo o equipo afectado: OtrosPRUEBA\nRuta por la que ingresa: Prueba\nTelefonos de afectado: 1234567\nUbicación del afectado  (Sede y piso): Prueba\nNombre completo del afectado: <PERSON> con el que ingresó: Maripoda\nDescripción del error: <PERSON><PERSON><PERSON>, favor no gestionar, gracias\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 43054\\nDWP_SRInstanceID: AGGJYA2JFNGSXASVIXC7SVIXC7KY0N\\nIncident Number: INC000000033606\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000125;1000000478;'maripoda';", "last_modified_date": "2025-06-16T22:03:26.000+0000", "create_date": "2025-04-24T23:25:36.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "43054", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Prueba", "last_modified_date": "2025-06-16T22:03:26.000+0000"}}, {"incident_id": "INC000000033607", "status": "Cancelled", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: \nAplicativo o equipo afectado: Somos Sura\nRuta por la que ingresa: Prueba\nTelefonos de afectado: 1234567\nUbicación del afectado  (Sede y piso): Prueba\nNombre completo del afectado: <PERSON> con el que ingresó: Maripoda\nDescripción del error: <PERSON><PERSON><PERSON>, favor no gestionar, gracias\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 43055\\nDWP_SRInstanceID: AGGJYA2JFNGSXASVIXE0SVIXE0KYQO\\nIncident Number: INC000000033607\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000125;1000000478;'maripoda';", "last_modified_date": "2025-06-16T22:00:37.000+0000", "create_date": "2025-04-24T23:26:39.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "43055", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Prueba", "last_modified_date": "2025-06-16T22:00:36.000+0000"}}, {"incident_id": "INC000000033608", "status": "Cancelled", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: \nAplicativo o equipo afectado: VPN\nRuta por la que ingresa: Prueba\nTelefonos de afectado: 1234567\nUbicación del afectado  (Sede y piso): Prueba\nNombre completo del afectado: <PERSON> con el que ingresó: Maripoda\nDescripción del error: <PERSON><PERSON><PERSON>, favor no gestionar, gracias\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 43056\\nDWP_SRInstanceID: AGGJYA2JFNGSXASVIXG4SVIXG4KZOH\\nIncident Number: INC000000033608\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000125;1000000478;'maripoda';", "last_modified_date": "2025-05-20T21:47:22.000+0000", "create_date": "2025-04-24T23:27:56.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Prueba - No mostró plantilla de resolución.", "resolution_method": null, "dwp_number": "43056", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Prueba.", "last_modified_date": "2025-05-20T21:47:22.000+0000"}}, {"incident_id": "INC000000033593", "status": "Closed", "description": "Errores funcionales - CORE", "detailed_description": "Aplicativo Afectado: ErroresfuncionalesCORE\nNombre completo del afectado: <PERSON>\nUsuario de red: Maripoda\nTeléfono del afectado: **********\nCorreo del afectado: <EMAIL>\nUbicación del afectado: Prueba\nRuta por la que ingresa: Prueba\nProducto / Proceso: Sinco -ARL (Microstrategy)\nOperación: No puede ingresar\nParámetros de consulta, opciones o filtros: Prueba\nReporte con inconsistencia: \nProceso: \nNumero de caso bandeja: \nNumero de convenio / número de cotización: \nNumero autorización: \nAplicación: \nModulo / Proceso: \nTipo de documento del afliado / proveedor: \nNumero siniestro / Poliza: \nDocumento del afiliado / Proveedor: \nDescripción del error: Prueba, no gestionar gracias.\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \nCual es el numero de PQRS: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 43047\\nDWP_SRInstanceID: AGGJYA2JFNGSXASVIWJUSVIWJUKNZS\\nIncident Number: INC000000033593\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000293;1000000478;'maripoda';", "last_modified_date": "2025-06-23T07:00:02.000+0000", "create_date": "2025-04-24T23:09:44.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Prueba", "resolution_method": null, "dwp_number": "43047", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nPrueba\n\nCategoría de resolución:\nGestión BMC.Pruebas PDN.Consola", "last_modified_date": "2025-06-17T14:10:14.000+0000"}}, {"incident_id": "INC000000033594", "status": "Cancelled", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: \nAplicativo o equipo afectado: Asistah\n<PERSON> por la que ingresa: Prueba\nTelefonos de afectado: 123456\nUbicación del afectado  (Sede y piso): Prueba\nNombre completo del afectado: <PERSON> con el que ingresó: Maripoda\nDescripción del error: <PERSON><PERSON><PERSON>, favor no gestionar gracias.\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 43048\\nDWP_SRInstanceID: AGGJYA2JFNGSXASVIWMPSVIWMPKSDF\\nIncident Number: INC000000033594\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000125;1000000478;'maripoda';", "last_modified_date": "2025-06-17T16:39:48.000+0000", "create_date": "2025-04-24T23:16:41.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "43048", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Prueba", "last_modified_date": "2025-06-17T16:39:48.000+0000"}}, {"incident_id": "INC000000033595", "status": "Cancelled", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: \nAplicativo o equipo afectado: Correo electrónico\nRuta por la que ingresa: \nTelefonos de afectado: \nUbicación del afectado  (Sede y piso): \nNombre completo del afectado: PRUEBA\nUsuario con el que ingresó: \nDescripción del error: <PERSON><PERSON><PERSON>, favor no gestionar gracias.\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 43049\\nDWP_SRInstanceID: AGGJYA2JFNGSXASVIWQGSVIWQGKT55\\nIncident Number: INC000000033595\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000125;1000000478;'maripoda';", "last_modified_date": "2025-06-17T16:38:51.000+0000", "create_date": "2025-04-24T23:18:26.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "43049", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Prueba", "last_modified_date": "2025-06-17T16:38:51.000+0000"}}, {"incident_id": "INC000000033596", "status": "Cancelled", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: \nAplicativo o equipo afectado: Doble Autenticación\nRuta por la que ingresa: PRUEBA\nTelefonos de afectado: 123456\nUbicación del afectado  (Sede y piso): PRUEBA\nNombre completo del afectado: PRUEBA\nUsuario con el que ingresó: Maripoda\nDescripción del error: <PERSON><PERSON><PERSON>, favor no gestionar gracias.\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 43051\\nDWP_SRInstanceID: AGGJYA2JFNGSXASVIWURSVIWURKVBZ\\nIncident Number: INC000000033596\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000125;1000000478;'maripoda';", "last_modified_date": "2025-06-17T16:38:16.000+0000", "create_date": "2025-04-24T23:21:14.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "43051", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Prueba", "last_modified_date": "2025-06-17T16:38:16.000+0000"}}, {"incident_id": "INC000000033698", "status": "Cancelled", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: \nAplicativo o equipo afectado: Equipos de computoPrueba\nRuta por la que ingresa: Prueba\nTelefonos de afectado: 12345\nUbicación del afectado  (Sede y piso): Prueba\nNombre completo del afectado: Prueba\nUsuario con el que ingresó: Prueba\nDescripción del error: Favor no gestionar, gracias.\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 43052\\nDWP_SRInstanceID: AGGJYA2JFNGSXASVIWX9SVIWX9KW05\\nIncident Number: INC000000033698\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000125;1000000478;'maripoda';", "last_modified_date": "2025-06-17T16:36:39.000+0000", "create_date": "2025-04-24T23:22:56.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "43052", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Prueba", "last_modified_date": "2025-06-17T16:36:39.000+0000"}}, {"incident_id": "INC000000033700", "status": "Cancelled", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: \nAplicativo o equipo afectado: Impresora\nRuta por la que ingresa: Prueba\nTelefonos de afectado: 1234567\nUbicación del afectado  (Sede y piso): Prueba\nNombre completo del afectado: <PERSON> con el que ingresó: Maripoda\nDescripción del error: <PERSON><PERSON><PERSON>, favor no gestionar, gracias\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Equipment\\nHPD_CI_ReconID: OI-4D867F32539A11EFB3581AF001D8BC3E\\nHPD_CI_FormName: BMC_EQUIPMENT\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Impresora\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 43053\\nDWP_SRInstanceID: AGGJYA2JFNGSXASVIWZXSVIWZXKXIG\\nIncident Number: INC000000033700\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000125;1000000478;'maripoda';", "last_modified_date": "2025-05-12T21:36:30.000+0000", "create_date": "2025-04-24T23:24:22.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "43053", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Prueba - Cancelado por solicitud de usuario.", "last_modified_date": "2025-05-12T21:36:29.000+0000"}}, {"incident_id": "INC000000034899", "status": "Closed", "description": "Reporte de fallla", "detailed_description": "Aplicacion / Modulo afectado: PortalServiciosTecnologia\nTipo de error: <PERSON><PERSON><PERSON>/Falla\nNombre Usuario Afectado: <PERSON>\nUsuario afectado: Maripoda\nContacto usuario afectado: **********\nDescripción del error: Cuando se registra una petición queda en estado \"Finalizado\" y no crea INC, favor validar. Para estas preguntas los incidentes deben quedar marcados como críticos, que fue la solicitud realizada en el RG 55, gracias.\n1.  Ayudas DX Laboratorio - ¿Toda el área del proceso reportado en la sede está afectado?\n2. Ayudas DX RIS-PACS - ¿El error reportado impacta de forma completa el servicio del proceso afectado en la sede?\n3. Health Cloud (PHM) - IPS Especializada", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Incidente\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Hallazgo en Estabilización\\nDWP_SRID: 44802\\nDWP_SRInstanceID: AGGJEQF5JXMXIASV5LA7SV5LA7T4TE\\nIncident Number: INC000000034899\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Plataforma ITSM BMC\\nProduct Categorization Tier 2: Plataforma ITSM BMC\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "**********;'maripoda';", "last_modified_date": "2025-05-23T07:00:00.000+0000", "create_date": "2025-04-28T13:51:58.000+0000", "priority": "Medium", "impact": "3-Moderate/Limited", "urgency": "3-Medium", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Buenas tardes, como están\n\nSe realiza ajuste en Qa y Prod del registro 55, estado Sonda > ajustado pdn y qa\n\nLa plantilla Ayudas DX Laboratorio - ¿Toda el área del proceso reportado en la sede está afectado? No está marcando el caso como critico\n\nen el correo de dejar los casos de prueba.", "resolution_method": null, "dwp_number": "44802", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nBuenas tardes, como están\n\nSe realiza ajuste en Qa y Prod del registro 55, estado Sonda > ajustado pdn y qa\n\nLa plantilla Ayudas DX Laboratorio - ¿Toda el área del proceso reportado en la sede está afectado? No está marcando el caso como critico\n\nen el correo de dejar los casos de prueba.\n\nCategoría de resolución:\nGestión BMC.Pruebas PDN.Consola", "last_modified_date": "2025-05-17T20:17:37.000+0000"}}, {"incident_id": "INC000000034933", "status": "Cancelled", "description": "Ayudas DX Laboratorio", "detailed_description": "Aplicativo Afectado: AyudasDXLaboratorio\nNombre completo del afectado: <PERSON>\nUsuario de red: Maripoda\nTeléfono del afectado: **********\nCorreo del afectado: <EMAIL>\nUbicación del afectado: Prueba\nRuta por la que ingresa: Prueba\nTelefono de contacto secundario: **********\nProceso afectado: Analítico (procesamiento y transmisión)\nSubproceso: Carga de listas de trabajo al analizador\nAplicación por la que ingresa: SOAD\nSección de procesamineto: Prueba\nEquipo analizador y casa comercial: Prueba\nCantidad de resultados afectados: Más de 1000\nCedulas / Ordenes de referencia: 12345678\n¿Toda el área del proceso reportado en la sede está afectado?: No\nDescripción del error: Prue<PERSON>, favor no gestionar.\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 44849\\nDWP_SRInstanceID: AGGJEQF5JXMXIASV5N2OSV5N2OW6WI\\nIncident Number: INC000000034933\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000133;1000000478;'maripoda';", "last_modified_date": "2025-06-16T21:58:36.000+0000", "create_date": "2025-04-28T14:38:54.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "44849", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Prueba", "last_modified_date": "2025-06-16T21:58:35.000+0000"}}, {"incident_id": "INC000000034934", "status": "Closed", "description": "Ayudas DX RIS-PACS", "detailed_description": "Nombre completo del afectado: <PERSON>\nUsuario de red: Maripoda\nTeléfono del afectado: **********\nCorreo del afectado: <EMAIL>\nUbicación del afectado: Prueba\nRuta por la que ingresa: Prueba\nTelefono de contacto secundario: 123456\nProceso afectado: Estudios en estado pending\nNúmero de cedula paciente y número de acceso PACS: Prueba\n¿El error reportado impacta de forma completa el servicio del proceso afectado en la sede?(Radio (No | Si): No\nDescripción del error: Prue<PERSON>, favor no gestionar.\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 44743\\nDWP_SRInstanceID: AGGD9PZXAOXN3ASV5N3KSV5N3KXQ0H\\nIncident Number: INC000000034934\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000133;1000000478;'maripoda';", "last_modified_date": "2025-05-12T07:00:10.000+0000", "create_date": "2025-04-28T14:39:25.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Prueba resolución, reapertura DWP.", "resolution_method": null, "dwp_number": "44743", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nPrueba resolución, reapertura DWP.\n\nCategoría de resolución:\nGestión BMC.Pruebas PDN.Consola", "last_modified_date": "2025-05-06T19:16:19.000+0000"}}, {"incident_id": "INC000000034935", "status": "Closed", "description": "Health Cloud (PHM)", "detailed_description": "Nombre completo del afectado: <PERSON>\nUsuario de red: Maripoda\nTeléfono del afectado: **********\nCorreo del afectado: <EMAIL>\nUbicación del afectado (sede y piso): Prueba\nRuta por la que ingresa: Prueba\nProceso afectado: Error dentro de la aplicación\nIPS Especializada: No\nDescripción del error: <PERSON><PERSON><PERSON>, favor no gestionar.\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-02D5D918892011EF89945E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: APLICATIVOS\\nClosure Product Category Tier1: Health Cloud - PHM\\nResolution Category Tier 2: CAPACITACION\\nClosure Product Category Tier2: Empresas\\nHPD_CI: Health Cloud - PHM\\nClosure Product Category Tier3: Prevencion Empresas\\nDWP_SRID: 44850\\nDWP_SRInstanceID: AGGJEQF5JXMXIASV5N4CSV5N4CW7PK\\nIncident Number: INC000000034935\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Health Cloud - PHM\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: Health Cloud - PHM\\nProduct Categorization Tier 3: Prevencion Empresas\\nProduct Categorization Tier 2: Empresas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "**********;'maripoda';", "last_modified_date": "2025-05-04T07:02:00.000+0000", "create_date": "2025-04-28T14:39:42.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Buenas tardes,\n\nSe resuelve el incidente, ya que se espero el tiempo para no gestionar el incidente de prueba pero nadie se hizo cargo lo cual se procede con el cierre del mismo.", "resolution_method": null, "dwp_number": "44850", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nBuenas tardes,\n\nSe resuelve el incidente, ya que se espero el tiempo para no gestionar el incidente de prueba pero nadie se hizo cargo lo cual se procede con el cierre del mismo.\n\nCategoría de resolución:\nCAPACITACION.CAPACITACION.APLICATIVOS", "last_modified_date": "2025-04-28T21:52:35.000+0000"}}, {"incident_id": "INC000000036053", "status": "Closed", "description": "Aplicación - Sintoma", "detailed_description": "Aplicativo o equipo afectado: Prueba Inc Mayor\nDescripción del error o falla: \nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 46154\\nIncident Number: INC000000036053\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000522;1000000478;'maripoda';", "last_modified_date": "2025-05-05T07:02:56.000+0000", "create_date": "2025-04-29T19:03:49.000+0000", "priority": "Low", "impact": "2-Significant/Large", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente):\nSolución:\nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO):\nPrueba", "resolution_method": null, "dwp_number": "46154", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nCausa (Motivo, origen o razón de que suceda el incidente):\nSolución:\nCausa Raíz (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO):\nPrueba\n\nCategoría de resolución:\nGestión BMC.Pruebas PDN.Consola", "last_modified_date": "2025-04-29T19:44:42.000+0000"}}, {"incident_id": "INC000000036505", "status": "Closed", "description": "Ayudas DX Laboratorio", "detailed_description": "Aplicativo Afectado: AyudasDXLaboratorio\nNombre completo del afectado: <PERSON>\nUsuario de red: Maripoda\nTeléfono del afectado: **********\nCorreo del afectado: <EMAIL>\nUbicación del afectado: Prueba\nRuta por la que ingresa: Prueba\nTelefono de contacto secundario: **********\nProceso afectado: Analítico (procesamiento y transmisión)\nSubproceso: Carga de listas de trabajo al analizador\nAplicación por la que ingresa: SOAD\nSección de procesamineto: Prueba\nEquipo analizador y casa comercial: Prueba\nCantidad de resultados afectados: Más de 1000\nCedulas / Ordenes de referencia: 12345678\n¿Toda el área del proceso reportado en la sede está afectado?: Si\nDescripción del error: Prue<PERSON>, favor no gestionar.\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 46640\\nDWP_SRInstanceID: AGGD9PZXAOXN3ASV9I4QSV9I4Q7QE3\\nIncident Number: INC000000036505\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000133;1000000478;'maripoda';", "last_modified_date": "2025-05-12T07:00:19.000+0000", "create_date": "2025-04-30T13:06:35.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Prueba resolución, reapertura DWP.", "resolution_method": null, "dwp_number": "46640", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nPrueba resolución, reapertura DWP.\n\nCategoría de resolución:\nGestión BMC.Pruebas PDN.Consola", "last_modified_date": "2025-05-06T19:16:19.000+0000"}}, {"incident_id": "INC000000036506", "status": "Cancelled", "description": "Ayudas DX RIS-PACS", "detailed_description": "Nombre completo del afectado: <PERSON>\nUsuario de red: Maripoda\nTeléfono del afectado: **********\nCorreo del afectado: <EMAIL>\nUbicación del afectado: Prueba\nRuta por la que ingresa: Prueba\nTelefono de contacto secundario: 123456\nProceso afectado: Estudios en estado pending\nNúmero de cedula paciente y número de acceso PACS: Prueba\n¿El error reportado impacta de forma completa el servicio del proceso afectado en la sede?(Radio (No | Si): Si\nDescripción del error: <PERSON>rue<PERSON>, favor no gestionar.\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-026B5B10892011EF89355E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nClosure Product Category Tier1: Somos Sura\\nClosure Product Category Tier2: Tranversal\\nHPD_CI: SomosSura\\nClosure Product Category Tier3: Tranversal\\nDWP_SRID: 46642\\nDWP_SRInstanceID: AGGD9PZXAOXN3ASV9I6LSV9I6L7SCJ\\nIncident Number: INC000000036506\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: EPS\\nCategorization Tier 2: Gestion Comercial Somos Sura\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: Somos Sura\\nProduct Categorization Tier 3: Tranversal\\nProduct Categorization Tier 2: Tranversal\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000133;1000000478;'maripoda';", "last_modified_date": "2025-04-30T13:17:56.000+0000", "create_date": "2025-04-30T13:07:37.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "46642", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Buen día,\n\nSe procede a la cancelación del caso por solicitud del usuario.\n\nFeliz día.", "last_modified_date": "2025-04-30T13:17:56.000+0000"}}, {"incident_id": "INC000000036513", "status": "Cancelled", "description": "Health Cloud (PHM)", "detailed_description": "Nombre completo del afectado: <PERSON>\nUsuario de red: Maripoda\nTeléfono del afectado: **********\nCorreo del afectado: <EMAIL>\nUbicación del afectado (sede y piso): Prueba\nRuta por la que ingresa: Prueba\nProceso afectado: Error dentro de la aplicación\nIPS Especializada: Si\nDescripción del error: <PERSON><PERSON><PERSON>, favor no gestionar.\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-02C161EA892011EF89835E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nClosure Product Category Tier1: IPSA\\nClosure Product Category Tier2: Archivo\\nHPD_CI: IPSA\\nClosure Product Category Tier3: Archivo\\nDWP_SRID: 46546\\nDWP_SRInstanceID: AGGJEQF5JXMXIASV9IM2SV9IM20XO0\\nIncident Number: INC000000036513\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: EPS\\nCategorization Tier 2: IPSA\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: IPSA\\nProduct Categorization Tier 3: Archivo\\nProduct Categorization Tier 2: Archivo\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "**********;'maripoda';", "last_modified_date": "2025-04-30T13:39:39.000+0000", "create_date": "2025-04-30T13:11:02.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "46546", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Ce cancela soporte de prueba", "last_modified_date": "2025-04-30T13:39:39.000+0000"}}, {"incident_id": "INC000000038399", "status": "Cancelled", "description": "Plantilla General", "detailed_description": "Aplicativo o equipo afectado: \nDescripción del error o falla: \nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-03D0A2A8892011EF8A795E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Azure DevOps\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 48732\\nIncident Number: INC000000038399\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000478;'maripoda';", "last_modified_date": "2025-05-08T16:07:26.000+0000", "create_date": "2025-05-05T15:05:08.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO):", "resolution_method": null, "dwp_number": "48732", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000040770", "status": "Cancelled", "description": "Plantilla General - Pueba Notificación", "detailed_description": "Prueba notificaicón", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 51931\\nIncident Number: INC000000040770\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000124;1000000478;'maripoda';", "last_modified_date": "2025-05-12T21:23:58.000+0000", "create_date": "2025-05-08T19:50:47.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO):", "resolution_method": null, "dwp_number": "51931", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Cancelación - Duplicidad", "last_modified_date": "2025-05-12T21:23:57.000+0000"}}, {"incident_id": "INC000000042453", "status": "Cancelled", "description": "Petición Hija relacionada a la afectación mayor 53382", "detailed_description": "Aplicativo afectado: Agendaweb\nRuta por la que ingresa: Prueba\nOperación que estaba realizando: Prueba\nNombre del afectado: Maripoda\nTeléfono del afectado: 123456\nUsuario con el que ingresó a la aplicación: Prueba\nDescripción del error o falla: Prueba", "technical_info": "Associated Alarm: None\\nClosure Product Category Tier1: AgendaWeb\\nClosure Product Category Tier2: Administracion Solucion\\nClosure Product Category Tier3: Administracion Solucion\\nDWP_SRID: 53790\\nDWP_SRInstanceID: AGGFVQAEC86NZASWFVCKSWFVCKIU16\\nIncident Number: INC000000042453\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: IPS\\nCategorization Tier 2: AgendaWeb\\nCategorization Tier 3: Bloqueos-Lentitudes\\nProduct Categorization Tier 1: AgendaWeb\\nProduct Categorization Tier 3: Administracion Solucion\\nProduct Categorization Tier 2: Administracion Solucion\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000298;'maripoda';", "last_modified_date": "2025-05-12T18:29:46.000+0000", "create_date": "2025-05-12T18:25:40.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "53790", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000043452", "status": "Cancelled", "description": "Plantilla General", "detailed_description": "Prueba", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 55233\\nIncident Number: INC000000043452\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000478;'maripoda';", "last_modified_date": "2025-05-13T21:21:43.000+0000", "create_date": "2025-05-13T21:12:30.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO):", "resolution_method": null, "dwp_number": "55233", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Prueba", "last_modified_date": "2025-05-13T21:21:43.000+0000"}}, {"incident_id": "INC000000046824", "status": "Cancelled", "description": "Prueba - Estado suspensión: Intervención tercero", "detailed_description": "Aplicativo o equipo afectado: \nDescripción del error o falla: \nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 59171\\nIncident Number: INC000000046824\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Request", "assigned_to": null, "assignee_groups": "1000000545;1000000478;'maripoda';", "last_modified_date": "2025-06-16T21:57:01.000+0000", "create_date": "2025-05-19T15:41:39.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Request", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO):", "resolution_method": null, "dwp_number": "59171", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Prueba", "last_modified_date": "2025-06-16T21:57:01.000+0000"}}, {"incident_id": "INC000000046873", "status": "Cancelled", "description": "Prueba - <PERSON><PERSON>", "detailed_description": "Aplicativo Afectado DWP: \nAplicativo o equipo afectado: OtrosPrueba\nRuta por la que ingresa: Prueba\nTelefonos de afectado: 123456\nUbicación del afectado  (Sede y piso): Prueba\nNombre completo del afectado: Prueba\nUsuario con el que ingresó: Prueba\nDescripción del error: Prueba - Favor omitir\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 59344\\nDWP_SRInstanceID: AGGJYG8IF3BYEASW8OO4SW8OO4HW5X\\nIncident Number: INC000000046873\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000125;1000000478;'maripoda';", "last_modified_date": "2025-06-16T21:56:07.000+0000", "create_date": "2025-05-19T16:34:26.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "59344", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Prueba", "last_modified_date": "2025-06-16T21:56:06.000+0000"}}, {"incident_id": "INC000000047569", "status": "Closed", "description": "Otras Aplicaciones(Ipsa, phc, salud Web, etc)", "detailed_description": "Nombre completo del afectado: <PERSON>\nUsuario de red: Maripoda\nTeléfono del afectado: **********\nCorreo del afectado: <EMAIL>\nUbicación del afectado (sede y piso): Prueba\nRuta por la que ingresa: Prueba\nAplicativo afectado: Prueba\nDescripción del error: <PERSON>rueba - <PERSON><PERSON><PERSON>\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 60326\\nDWP_SRInstanceID: AGGJYG8IF3BYEASWK4PVSWK4PVN193\\nIncident Number: INC000000047569\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000133;1000000478;'maripoda';", "last_modified_date": "2025-06-09T07:00:07.000+0000", "create_date": "2025-05-20T14:54:42.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Prueba", "resolution_method": null, "dwp_number": "60326", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nPrueba\n\nCategoría de resolución:\nGestión BMC.Pruebas PDN.Consola", "last_modified_date": "2025-06-03T18:42:12.000+0000"}}, {"incident_id": "INC000000047570", "status": "Cancelled", "description": "Agenda Web", "detailed_description": "Nombre completo del afectado: <PERSON>\nUsuario de red: Maripoda\nTeléfono del afectado: **********\nCorreo del afectado: <EMAIL>\nUbicación del afectado (sede y piso): Prueba\nRuta por la que ingresa: Prueba\nProceso afectado: Errores periodo de atención\nDescripción del error: Prueba - Omitir\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 60222\\nDWP_SRInstanceID: AGGHE4WHFZM13ASWK4RTSWK4RTAD65\\nIncident Number: INC000000047570\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000125;1000000478;'maripoda';", "last_modified_date": "2025-06-16T21:55:14.000+0000", "create_date": "2025-05-20T14:55:16.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "60222", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Prueba", "last_modified_date": "2025-06-16T21:55:14.000+0000"}}, {"incident_id": "INC000000047600", "status": "Cancelled", "description": "OPC - AYUDAS DX", "detailed_description": "Nombre completo del afectado: <PERSON>\nUsuario de red: Maripoda\nTeléfono del afectado: **********\nCorreo del afectado: <EMAIL>\nUbicación del afectado (sede y piso): Prueba\nRuta por la que ingresa: Prueba\nProceso afectado: Anulaciones DX\nMensaje del error que presenta: Prueba\nNúmero de búsqueda: 1\nDato de búsqueda: Factura\nDescripción del error: Prueba - Omitir\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-01924D34892011EF887D5E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nClosure Product Category Tier1: SAP\\nClosure Product Category Tier2: OPC – Dinamica\\nHPD_CI: SAP-AP\\nClosure Product Category Tier3: Navegacion y Configuracion\\nDWP_SRID: 60223\\nDWP_SRInstanceID: AGGHE4WHFZM13ASWK4SLSWK4SLAEH3\\nIncident Number: INC000000047600\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: SAP\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: SAP\\nProduct Categorization Tier 3: Navegacion y Configuracion\\nProduct Categorization Tier 2: OPC – Dinamica\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000134;'maripoda';", "last_modified_date": "2025-05-20T15:05:47.000+0000", "create_date": "2025-05-20T14:55:50.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "60223", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: *Se cancela ya que son pruebas que se están realizando.", "last_modified_date": "2025-05-20T15:05:47.000+0000"}}, {"incident_id": "INC000000049080", "status": "Closed", "description": "Reporte de fallla", "detailed_description": "Aplicacion / Modulo afectado: ConsolaEspecialista\nTipo de error: Hallazgo en Estabilización\nNombre Usuario Afectado: <PERSON><PERSON>strada\nUsuario afectado: Linaesca\nContacto usuario afectado: **********\nDescripción del error: <PERSON>uen día, su ayuda por favor. No se deberían crear análisis forense (WO Forense) cuando se cierran los incidentes mayores con categoría de resolución GESTION DE PROVEEDORES > INTERVENCION > TERCERO, gracias. En el caso INC INC000000040899 se creo la WO WO0000000001703 y no debería, gracias. \n\nEn este caso el equipo de Forense lo canceló, el deber ser es que no se cree, gracias.", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Plataforma ITSM BMC\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Plataforma ITSM BMC\\nDWP_SRID: 62371\\nDWP_SRInstanceID: AGGHE4WHFZM13ASWOE6LSWOE6LCU0Z\\nIncident Number: INC000000049080\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Mensaje Error/Falla\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "**********;'maripoda';", "last_modified_date": "2025-06-11T07:00:08.000+0000", "create_date": "2025-05-22T15:10:49.000+0000", "priority": "Medium", "impact": "3-Moderate/Limited", "urgency": "3-Medium", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): Uso inadecuado de categoría de resolución en el cierre del incidente la cual permite que se cree el análisis forense\nSolución: Uso debido de las cat de resolución para que no se disparen los analisis forenses al momento de resolver el IM\nCausa Raíz (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): Si", "resolution_method": null, "dwp_number": "62371", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nCausa (Motivo, origen o razón de que suceda el incidente): Uso inadecuado de categoría de resolución en el cierre del incidente la cual permite que se cree el análisis forense\nSolución: Uso debido de las cat de resolución para que no se disparen los analisis forenses al momento de resolver el IM\nCausa Raíz (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): Si\n\nCategoría de resolución:\nGestión BMC.Pruebas PDN.Consola", "last_modified_date": "2025-06-05T19:32:16.000+0000"}}, {"incident_id": "INC000000049095", "status": "Cancelled", "description": "Ayudas DX Laboratorio", "detailed_description": "Aplicativo Afectado: AyudasDXLaboratorio\nNombre completo del afectado: <PERSON>\nUsuario de red: Maripoda\nTeléfono del afectado: **********\nCorreo del afectado: <EMAIL>\nUbicación del afectado: Prueba\nRuta por la que ingresa: Prueba\nTelefono de contacto secundario: **********\nProceso afectado: Analítico (procesamiento y transmisión)\nSubproceso: Carga de listas de trabajo al analizador\nAplicación por la que ingresa: SOAD\nSección de procesamineto: Prueba\nEquipo analizador y casa comercial: Prueba\nCantidad de resultados afectados: 2 a 100\nCedulas / Ordenes de referencia: 12345678\n¿Toda el área del proceso reportado en la sede está afectado?: No\nDescripción del error: Prueba criticidad\nTodo el area esta afectada: Si\nPQRS: No\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: Si\nMedio de Ingreso: Derechos de petición\n¿Cuál es el numero de la PQRS?: 123", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-03D80B2E892011EF8A825E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nClosure Product Category Tier1: SOAD\\nClosure Product Category Tier2: SOAD\\nHPD_CI: SOAD\\nClosure Product Category Tier3: SOAD\\nDWP_SRID: 62486\\nDWP_SRInstanceID: AGGJYG8IF3BYEASWOFVASWOFVAK7ZE\\nIncident Number: INC000000049095\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: DIN\\nCategorization Tier 2: SOAD\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: SOAD\\nProduct Categorization Tier 3: SOAD\\nProduct Categorization Tier 2: SOAD\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000133;1000000478;'maripoda';", "last_modified_date": "2025-05-27T18:48:52.000+0000", "create_date": "2025-05-22T15:33:42.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "62486", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Prueba", "last_modified_date": "2025-05-27T18:48:51.000+0000"}}, {"incident_id": "INC000000049192", "status": "Cancelled", "description": "Ayudas DX Laboratorio", "detailed_description": "Aplicativo Afectado: AyudasDXLaboratorio\nNombre completo del afectado: <PERSON>\nUsuario de red: Maripoda\nTeléfono del afectado: **********\nCorreo del afectado: <EMAIL>\nUbicación del afectado: Prueba\nRuta por la que ingresa: Prueba\nTelefono de contacto secundario: **********\nProceso afectado: Analítico (procesamiento y transmisión)\nSubproceso: Carga de listas de trabajo al analizador\nAplicación por la que ingresa: SOAD\nSección de procesamineto: Prueba\nEquipo analizador y casa comercial: Prueba\nCantidad de resultados afectados: 2 a 100\nCedulas / Ordenes de referencia: 12345678\n¿Toda el área del proceso reportado en la sede está afectado?: Si\nDescripción del error: Prueba criticidad\nTodo el area esta afectada: Si\nPQRS: No\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 62476\\nDWP_SRInstanceID: AGGJYG8IF3BYEASWOF99SWOF99KEZ0\\nIncident Number: INC000000049192\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000133;1000000478;'maripoda';", "last_modified_date": "2025-06-16T21:54:09.000+0000", "create_date": "2025-05-22T15:27:15.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "62476", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Prueba", "last_modified_date": "2025-06-16T21:54:09.000+0000"}}, {"incident_id": "INC000000049530", "status": "Cancelled", "description": "Automatización", "detailed_description": "Nombre completo del afectado: <PERSON>\nUsuario de red: Maripoda\nTeléfono del afectado: **********\nCorreo del afectado: <EMAIL>\nUbicación del afectado: Prueba\nRuta por la que ingresa: Prueba\nNombre de la automatización afectada: Prueba\nDescripción del error: <PERSON>rueba - Favor omitir\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-02BA9518892011EF897D5E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Espacios para Ti\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Transversales\\nHPD_CI: Espacios para ti\\nClosure Product Category Tier3: Reserva puestos de trabajo\\nDWP_SRID: 63000\\nDWP_SRInstanceID: AGGJYG8IF3BYEASWOKQ2SWOKQ2DT15\\nIncident Number: INC000000049530\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Espacios para Ti\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: Espacios para Ti\\nProduct Categorization Tier 3: Reserva puestos de trabajo\\nProduct Categorization Tier 2: Transversales\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000333;'maripoda';", "last_modified_date": "2025-05-23T16:43:16.000+0000", "create_date": "2025-05-22T20:54:57.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "63000", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Caso de prueba.", "last_modified_date": "2025-05-23T16:43:15.000+0000"}}, {"incident_id": "INC000000055577", "status": "Cancelled", "description": "Cotizador- Empresarial", "detailed_description": "Aplicativo Afectado: CotizadorEmpresarial\nTipo de falla: \nProducto / Proceso: Nuevo\nProducto: Hogares Sura\nUsuario afectado: Maripoda\nCelular del afectado: 123456\nRuta por la que ingresa: Prueba\nCotización: 123456\nPoliza: \nRadicado y/o poliza: \n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: No\n¿La póliza tiene oneroso?: No\nDescripción del error: Hola, solicito su ayuda con la cotización **** , caso de prueba favor omitir.\n¿Cuántas personas hay afectadas?: 1 persona¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-031F7424892011EF89D95E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nClosure Product Category Tier1: Aplicaciones\\nClosure Product Category Tier2: Aplicaciones de negocio\\nHPD_CI: Cotizador de Autos\\nClosure Product Category Tier3: Aplicaciones de negocio\\nDWP_SRID: 70656\\nDWP_SRInstanceID: AGGHE4WHFZM13ASX2FCJSX2FCJ6EVM\\nIncident Number: INC000000055577\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Portal DWP\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: Portal DWP\\nProduct Categorization Tier 3: Portal DWP\\nProduct Categorization Tier 2: Portal DWP\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000126;'maripoda';", "last_modified_date": "2025-06-04T14:45:26.000+0000", "create_date": "2025-06-04T14:23:31.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "70656", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Se omite incidente por solicitud del usuario \"Prueba.\"", "last_modified_date": "2025-06-04T14:45:25.000+0000"}}, {"incident_id": "INC000000055919", "status": "Cancelled", "description": "Acceso único de solicitudes AUS", "detailed_description": "Aplicativo Afectado: AccesoUnicoSolicitudesAUS\nProducto/ Proceso: Movilidad AUS\nProducto: \nRadicado y/o poliza: 123456\nUsuario afectado: Maripoda\nCelular del afectado: 12345\nRuta por la que ingresa: Prueba\nDescripción del error: <PERSON><PERSON><PERSON>\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 71179\\nDWP_SRInstanceID: AGGJYG8IF3BYEASX29G8SX29G8UVU9\\nIncident Number: INC000000055919\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000092;1000000478;'maripoda';", "last_modified_date": "2025-06-16T21:46:35.000+0000", "create_date": "2025-06-04T19:24:10.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "71179", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Prueba", "last_modified_date": "2025-06-16T21:46:35.000+0000"}}, {"incident_id": "INC000000058675", "status": "Closed", "description": "Cotizador -  Movilidad", "detailed_description": "Aplicativo Afectado: CotizadorMovilidad\nProceso: Nuevo\nCotización: 123456\nUsuario afectado: Maripoda\nCelular usuario afectado: 123456\nRuta por la que ingresa: 12345\nRadicado y/o poliza: 2345678\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: No\nDescripción del error : <PERSON><PERSON><PERSON>, omitir.\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 74666\\nDWP_SRInstanceID: AGGHE4WHFZM13ASXNIX1SXNIX1B2KK\\nIncident Number: INC000000058675\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Portal DWP\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: Portal DWP\\nProduct Categorization Tier 3: Portal DWP\\nProduct Categorization Tier 2: Portal DWP\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;'maripoda';", "last_modified_date": "2025-06-16T07:02:19.000+0000", "create_date": "2025-06-10T14:18:25.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): .\nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO):", "resolution_method": null, "dwp_number": "74666", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nCausa (Motivo, origen o razón de que suceda el incidente): .\nSolución: \nCausa Raíz (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO):\n\nCategoría de resolución:\nGestión BMC.Pruebas PDN.Consola", "last_modified_date": "2025-06-10T14:30:57.000+0000"}}, {"incident_id": "INC000000058956", "status": "Cancelled", "description": "Cotizador - Hogar y PES", "detailed_description": "Aplicativo Afectado: CotizadorHogarPES\nProducto/ Proceso: Renovación\nProducto: PES Daños\nUsuario afectado: Maripoda\nCelular del afectado: **********\nRuta por la que ingresa: NOAPLICA\nPoliza: \nCotización: 123456\nRadicado y/o poliza: \n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: No\n¿La póliza tiene oneroso?: No\nDescripción del error: Prueba\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: REGJ66CSC076KASV8JHHSV8JHHZD43\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: GESTION CON USUARIO\\nClosure Product Category Tier1: Aplicaciones\\nResolution Category Tier 2: CONTACTO\\nClosure Product Category Tier2: Aplicaciones de negocio\\nHPD_CI: Cotizador PES Core\\nClosure Product Category Tier3: Aplicaciones de negocio\\nDWP_SRID: 75065\\nDWP_SRInstanceID: AGGJYG8IF3BYEASXNMQDSXNMQD3RNW\\nIncident Number: INC000000058956\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Portal DWP\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: Portal DWP\\nProduct Categorization Tier 3: Portal DWP\\nProduct Categorization Tier 2: Portal DWP\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000126;'maripoda';", "last_modified_date": "2025-06-10T21:20:23.000+0000", "create_date": "2025-06-10T19:15:59.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "75065", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Incidente de prueba", "last_modified_date": "2025-06-10T21:20:23.000+0000"}}, {"incident_id": "INC000000059000", "status": "Closed", "description": "Soporte BMC Helix", "detailed_description": "Aplicativo Afectado:Errores BMC Helix\nTipo de falla:Error en el Portal servicios de tecnología\nNombre usuario afectado:<PERSON><PERSON><PERSON><PERSON> Beltran\nUsuario de red del afectado:giovhebl\nContacto usuario afectado:**********\nDescripción del error:<PERSON><PERSON>, su ayuda revisando la siguiente novedad, cuando el usuario intenta reabrir el caso desde la notificación del correo electronico le solicita realizar un cambio de contraseña, por favor validar con el si es algo puntual de su perfil o una novedad generalizada, gracias.\n", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: REASIGNE PRIVILEGIOS\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: ADMINISTRACION BMC\\nClosure Product Category Tier2: Plataforma ITSM BMC\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Plataforma ITSM BMC\\nDWP_SRID: 75048\\nDWP_SRInstanceID: AGGJYG8IF3BYEASXNL3SSXNL3S2KPK\\nIncident Number: INC000000059000\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Plataforma ITSM BMC\\nProduct Categorization Tier 2: Plataforma ITSM BMC\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "**********;'maripoda';", "last_modified_date": "2025-06-16T07:02:59.000+0000", "create_date": "2025-06-10T18:47:01.000+0000", "priority": "Medium", "impact": "3-Moderate/Limited", "urgency": "3-Medium", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): usuario giovhebl tenia la condición de forzar la clave local cada que inicie la sesón, se le quita esta opción\n\nSolución: valide con mi usuario jhonmede y realice la misma prueba e inicialmente me salía el mismo mensaje que a Gio sin embargo cuando me quite esta opción ya me dejo reabrir el caso correctamente.\nCausa Raíz (Identificada/Sin identificar): \nConfirmar operatividad del usuario Afectado (SI/NO): <PERSON>", "resolution_method": null, "dwp_number": "75048", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nCausa (Motivo, origen o razón de que suceda el incidente): usuario giovhebl tenia la condición de forzar la clave local cada que inicie la sesón, se le quita esta opción\n\nSolución: valide con mi usuario jhonmede y realice la misma prueba e inicialmente me salía el mismo mensaje que a Gio sin embargo cuando me quite esta opción ya me dejo reabrir el caso correctamente.\nCausa Raíz (Identificada/Sin identificar): \nConfirmar operatividad del usuario Afectado (SI/NO): Si\n\nCategoría de resolución:\nSERVICIOS DE TECNOLOGIA.ADMINISTRACION BMC.REASIGNE PRIVILEGIOS", "last_modified_date": "2025-06-10T21:31:38.000+0000"}}, {"incident_id": "INC000000059124", "status": "Closed", "description": "Soporte BMC Helix", "detailed_description": "Aplicativo Afectado:Errores BMC Helix\nTipo de falla:Error en el Portal servicios de tecnología\nNombre usuario afectado:<PERSON>\nUsuario de red del afectado:Maripoda\nContacto usuario afectado:**********\nDescripción del error:<PERSON><PERSON>, cuando se suspende el caso desde la consola de tickets no se actualiza el estado \"Pendiente\" en el Portal, el caso de ejemplo 62476, gracias.\n", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: ESCALAMIENTO A FABRICA\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: ADMINISTRACION BMC\\nClosure Product Category Tier2: Incidente\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Error en la aplicación\\nDWP_SRID: 75268\\nDWP_SRInstanceID: AGGHE4WHFZM13ASXNS5VSXNS5VRBPS\\nIncident Number: INC000000059124\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Plataforma ITSM BMC\\nProduct Categorization Tier 2: Plataforma ITSM BMC\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "**********;'maripoda';", "last_modified_date": "2025-07-08T07:00:03.000+0000", "create_date": "2025-06-10T21:21:28.000+0000", "priority": "Medium", "impact": "3-Moderate/Limited", "urgency": "3-Medium", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): variable estándar no mapeada en todos los servicios\nSolución: Se garantizó la variable correlation ID en todos los servicios\nCausa Raíz (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): Si\n\nEnviado: lunes, 30 de junio de 2025 16:05\n<PERSON><PERSON>,\n\nUn gusto saludarte, es correcto son peticiones en las cuales la viriable \"correlation ID\" no estaba agregada. He revisado estos servicios y veo la variable ya mapeada correctamente por lo que ya no debería seguir ocurriendo este comportamiento.", "resolution_method": null, "dwp_number": "75268", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nCausa (Motivo, origen o razón de que suceda el incidente): variable estándar no mapeada en todos los servicios\nSolución: Se garantizó la variable correlation ID en todos los servicios\nCausa Raíz (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): Si\n\nEnviado: lunes, 30 de junio de 2025 16:05\n<PERSON><PERSON>,\n\nUn gusto saludarte, es correcto son peticiones en las cuales la viriable \"correlation ID\" no estaba agregada. He revisado estos servicios y veo la variable ya mapeada correctamente por lo que ya no debería seguir ocurriendo este comportamiento.\n\nCategoría de resolución:\nSERVICIOS DE TECNOLOGIA.ADMINISTRACION BMC.ESCALAMIENTO A FABRICA", "last_modified_date": "2025-07-02T13:23:44.000+0000"}}, {"incident_id": "INC000000059858", "status": "Closed", "description": "Soporte BMC Helix", "detailed_description": "Aplicativo Afectado:Errores BMC Helix\nTipo de falla:Error en el Portal servicios de tecnología\nNombre usuario afectado:<PERSON><PERSON><PERSON><PERSON> Beltran\nUsuario de red del afectado:giovhebl\nContacto usuario afectado:**********\nDescripción del error:<PERSON><PERSON>, su ayuda revisando la siguiente novedad, cuando el usuario intenta reabrir el caso desde la notificación del correo electrónico le solicita realizar un cambio de contraseña, esto quedo corregido supuestamente con el caso 75048, sin embargo realizando validaciones cuando intenta reabrir el caso de nuevo le aparece un error diferente, cajo de ejemplo 76318, por favor validar, gracias.\n", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: RETROALIMENTACION\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: ADMINISTRACION BMC\\nClosure Product Category Tier2: Plataforma ITSM BMC\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Plataforma ITSM BMC\\nDWP_SRID: 76249\\nDWP_SRInstanceID: AGGHE4WHFZM13ASXP9JASXP9JAWOQ5\\nIncident Number: INC000000059858\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Mensaje Error/Falla\\nProduct Categorization Tier 2: Incidente\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "**********;'maripoda';", "last_modified_date": "2025-06-18T07:01:30.000+0000", "create_date": "2025-06-11T20:33:55.000+0000", "priority": "Medium", "impact": "3-Moderate/Limited", "urgency": "3-Medium", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): ventanas emergentes en el navegador del usuario bloqueadas\nSolución: activar ventanas emergentes en el navegador del usuario\nCausa Raíz (Identificada/Sin identificar): \nConfirmar operatividad del usuario Afectado (SI/NO): Si", "resolution_method": null, "dwp_number": "76249", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nCausa (Motivo, origen o razón de que suceda el incidente): ventanas emergentes en el navegador del usuario bloqueadas\nSolución: activar ventanas emergentes en el navegador del usuario\nCausa Raíz (Identificada/Sin identificar): \nConfirmar operatividad del usuario Afectado (SI/NO): Si\n\nCategoría de resolución:\nSERVICIOS DE TECNOLOGIA.ADMINISTRACION BMC.RETROALIMENTACION", "last_modified_date": "2025-06-12T19:11:50.000+0000"}}, {"incident_id": "INC000000060076", "status": "Closed", "description": "Empresariales - Hogar y PES Core", "detailed_description": "Aplicativo Afectado: Empresariales - Hogar y PES Core\nNombre completo del afectado: Prueba\nUsuario de red: Prueba\nTeléfono del afectado (Celular, Fijo, Ext): 123456\nCorreo del afectado: <EMAIL>\nUbicación del afectado (sede y piso): Prueba\nRuta por la que ingresa: Prueba\nAsistentes afectados: \nCotizacion o Factura: 1234\nPóliza: 234\n¿Su inconveniente es con el ingreso al correo?: No\n¿La póliza tiene oneroso?: Si\nDescripción del error: Prueba\nEl reporte surge de una PQRS de Salesforce: No\nMedio de ingreso: \nCual es el número de la PQRS: \n", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 76638\\nDWP_SRInstanceID: AGGHE4WHFZM13ASXQVRDSXQVRDRX00\\nIncident Number: INC000000060076\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';", "last_modified_date": "2025-06-18T07:02:06.000+0000", "create_date": "2025-06-12T13:40:37.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Prueba", "resolution_method": null, "dwp_number": "76638", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nPrueba\n\nCategoría de resolución:\nGestión BMC.Pruebas PDN.Consola", "last_modified_date": "2025-06-12T13:42:29.000+0000"}}, {"incident_id": "INC000000062754", "status": "Cancelled", "description": "Empresariales - Hogar y PES Core", "detailed_description": "Aplicativo Afectado: Empresariales - Hogar y PES Core\nNombre completo del afectado: <PERSON> red: Maripoda\nTeléfono del afectado (Celular, Fijo, Ext): 123456\nCorreo del afectado: <EMAIL>\nUbicación del afectado (sede y piso): Prueba\nRuta por la que ingresa: Prueba\nAsistentes afectados: \nCotizacion o Factura: 12345678\nPóliza: 123456\n¿Su inconveniente es con el ingreso al correo?: No\n¿La póliza tiene oneroso?: No\nDescripción del error: Prueba detalle, descripción.\nEl reporte surge de una PQRS de Salesforce: No\nMedio de ingreso: \nCual es el número de la PQRS: \n", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-038B4DA2892011EF8A3B5E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Cotizador Hogar\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Propiedad\\nHPD_CI: Hogar C/S\\nClosure Product Category Tier3: Hogares Sura\\nDWP_SRID: 80032\\nDWP_SRInstanceID: AGGJYG8IF3BYEASYAHP4SYAHP4QOAM\\nIncident Number: INC000000062754\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Portal DWP\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: Portal DWP\\nProduct Categorization Tier 3: Portal DWP\\nProduct Categorization Tier 2: Portal DWP\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000126;'maripoda';", "last_modified_date": "2025-06-17T15:02:58.000+0000", "create_date": "2025-06-17T14:20:24.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "80032", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Incidente de prueba, se procede con el cierre.", "last_modified_date": "2025-06-17T15:02:58.000+0000"}}, {"incident_id": "INC000000063155", "status": "Cancelled", "description": "Simonweb", "detailed_description": "Aplicativo Afectado: Simonweb\n¿Cuántas personas hay afectadas?: 1 persona\nRuta por la que ingresa: Prueba\nProducto/Proceso: Prueba\nNúmero de póliza: 12\nNúmero de recibo o solicitud: 12\nCédula del afiliado: 123456\nUsuario afectado: Maripoda\nContacto del usuario: 123456\nDescripción del error: Detalle de la novedad del error\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 80602\\nDWP_SRInstanceID: AGGJYG8IF3BYEASYAL7JSYAL7J37EQ\\nIncident Number: INC000000063155\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000232;1000000478;'maripoda';", "last_modified_date": "2025-06-17T20:29:40.000+0000", "create_date": "2025-06-17T19:17:26.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "80602", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Prueba", "last_modified_date": "2025-06-17T20:29:40.000+0000"}}, {"incident_id": "INC000000067993", "status": "Cancelled", "description": "Prueba Mayor - <PERSON><PERSON><PERSON>", "detailed_description": "<PERSON><PERSON><PERSON> sintomas", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 86710\\nIncident Number: INC000000067993\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000478;'maripoda';", "last_modified_date": "2025-06-25T22:52:33.000+0000", "create_date": "2025-06-25T21:53:17.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Prueba", "resolution_method": null, "dwp_number": "86710", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Prueba", "last_modified_date": "2025-06-25T22:52:33.000+0000"}}, {"incident_id": "INC000000068310", "status": "Cancelled", "description": "Soporte BMC Helix", "detailed_description": "Aplicativo Afectado:Errores BMC Helix\nTipo de falla:Otro\nNombre usuario afectado:<PERSON>\nUsuario de red del afectado:Maripoda\nContacto usuario afectado:**********\nDescripción del error:Prueba Cancelación\n", "technical_info": "Associated Alarm: None\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nClosure Product Category Tier2: Plataforma ITSM BMC\\nClosure Product Category Tier3: Plataforma ITSM BMC\\nDWP_SRID: 87049\\nDWP_SRInstanceID: AGGJYG8IF3BYEASY6TRWSY6TRWG3WN\\nIncident Number: INC000000068310\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Plataforma ITSM BMC\\nProduct Categorization Tier 2: Plataforma ITSM BMC\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "**********;'maripoda';", "last_modified_date": "2025-06-26T13:37:14.000+0000", "create_date": "2025-06-26T13:36:33.000+0000", "priority": "Medium", "impact": "3-Moderate/Limited", "urgency": "3-Medium", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "87049", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000069291", "status": "In Progress", "description": "Soporte BMC Helix", "detailed_description": "Aplicativo Afectado:Errores BMC Helix\nTipo de falla:Error en la consola\nNombre usuario afectado:<PERSON>\nUsuario de red del afectado:Maripoda\nContacto usuario afectado:**********\nDescripción del error:Buen día, su ayuda por favor ajustando el estado con el que se inactivan los usuarios cuando se modifican desde la integración de Portales, deben quedar en estado \"archivado\" para garantizar que no se creen incidentes a nombre de personas inactivas, muchas gracias por su ayuda.\n\nAdjunto imagen de usuarios inactivos por portales en estado \"Sin Conexión\", información extraída de la base de datos de usuarios sin perfil.\n", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nHPD_CI: Plataforma ITSM BMC\\nDWP_SRID: 88133\\nDWP_SRInstanceID: AGGJYG8IF3BYEASY8VHLSY8VHLN98U\\nIncident Number: INC000000069291\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Plataforma ITSM BMC\\nProduct Categorization Tier 2: Plataforma ITSM BMC\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "**********;'maripoda';", "last_modified_date": "2025-07-15T15:39:27.000+0000", "create_date": "2025-06-27T16:59:31.000+0000", "priority": "Medium", "impact": "3-Moderate/Limited", "urgency": "3-Medium", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "88133", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Motivo de suspensión: Pruebas de usuario\n\nFecha de reactivación: 15/7/2025 9:00:00\n\nNota de suspensión: Pendiente de que el implementador realice la modificación en la integración", "last_modified_date": "2025-07-14T20:07:51.000+0000"}}, {"incident_id": "INC000000070184", "status": "Cancelled", "description": "Acceso único de solicitudes AUS", "detailed_description": "Aplicativo Afectado: AccesoUnicoSolicitudesAUS\nProducto/ Proceso: Movilidad AUS\nProducto: \nRadicado y/o poliza: 12345\nUsuario afectado: Maripoda\nCelular del afectado: 12345\nRuta por la que ingresa: Prueba\nDescripción del error: <PERSON><PERSON><PERSON>, detallados.\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 89281\\nDWP_SRInstanceID: AGGJYG8IF3BYEASYQHPASYQHPAHA09\\nIncident Number: INC000000070184\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000092;'maripoda';", "last_modified_date": "2025-07-02T16:23:54.000+0000", "create_date": "2025-07-01T15:20:01.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "89281", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Prueba", "last_modified_date": "2025-07-02T16:23:53.000+0000"}}, {"incident_id": "INC000000070201", "status": "Closed", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: \nAplicativo o equipo afectado: OtrosPrueba\nRuta por la que ingresa: Prueba\nTelefonos de afectado: 12345\nUbicación del afectado  (Sede y piso): Prueba\nNombre completo del afectado: <PERSON> con el que ingresó: Maripoda\nDescripción del error: Prueba\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\n", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 89236\\nDWP_SRInstanceID: AGGJYG8IF3BYEASYQGE4SYQGE4EVUO\\nIncident Number: INC000000070201\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000125;1000000478;'maripoda';", "last_modified_date": "2025-07-07T07:03:02.000+0000", "create_date": "2025-07-01T14:41:34.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Prueba, la solución del caso xxx.", "resolution_method": null, "dwp_number": "89236", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nPrueba, la solución del caso xxx.\n\nCategoría de resolución:\nGestión BMC.Pruebas PDN.Consola", "last_modified_date": "2025-07-01T14:43:16.000+0000"}}, {"incident_id": "INC000000070614", "status": "Closed", "description": "Pago Express", "detailed_description": "Aplicativo Afectado: PagoExpress\nOperación: Nuevo\nPóliza / Recibo: 12345\nUsuario afectado: Maripoda\nRuta por la que ingresa: Prueba\nDescripción del error: Detalle del error, usuario, error.\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-02366C84892011EF89055E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: USUARIOS\\nClosure Product Category Tier1: SAP\\nResolution Category Tier 2: PROCESO\\nClosure Product Category Tier2: OPC – Dinamica\\nHPD_CI: OPC\\nClosure Product Category Tier3: Navegacion y Configuracion\\nDWP_SRID: 89893\\nDWP_SRInstanceID: AGGJYG8IF3BYEASYQ9C0SYQ9C0OWNV\\nIncident Number: INC000000070614\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: Gestion Interna\\nCategorization Tier 2: SAP\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: SAP\\nProduct Categorization Tier 3: Navegacion y Configuracion\\nProduct Categorization Tier 2: OPC – Dinamica\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000134;'maripoda';", "last_modified_date": "2025-07-07T07:04:07.000+0000", "create_date": "2025-07-01T19:24:18.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "caso creado como Prueba", "resolution_method": null, "dwp_number": "89893", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\ncaso creado como Prueba\n\nCategoría de resolución:\nCAPACITACION.PROCESO.USUARIOS", "last_modified_date": "2025-07-01T19:42:56.000+0000"}}, {"incident_id": "INC000000072529", "status": "Cancelled", "description": "Interoperabilidad", "detailed_description": "Aplicativo afectado: Interoperabilidad\nNombre completo del afectado: <PERSON>ua<PERSON> de red: Maripoda\nTeléfono del afectado: **********\nCorreo del afectado: <EMAIL>\nUbicación del afectado: \nRuta por la que ingresa: \nNombre de la automatización afectada: \nDescripción del error: <PERSON><PERSON><PERSON>\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?: ", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 92304\\nDWP_SRInstanceID: AGGHE4WHFZM13ASYU0UZSYU0UZWOSN\\nIncident Number: INC000000072529\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000384;1000000478;'maripoda';", "last_modified_date": "2025-07-10T21:25:51.000+0000", "create_date": "2025-07-03T20:16:40.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): Time out en url xxx\nSolución: Por favor señor usuario, reinicie su equipo y luego desconectese y conectase a la VPN\nCausa Raíz (Identificada/Sin identificar): Identificada\nConfirmar operatividad del usuario Afectado (SI/NO): No", "resolution_method": null, "dwp_number": "92304", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Prueba", "last_modified_date": "2025-07-10T21:25:51.000+0000"}}, {"incident_id": "INC000000074272", "status": "Closed", "description": "Soporte BMC Helix", "detailed_description": "Aplicativo Afectado:Errores BMC Helix\nTipo de falla:Error en la consola\nNombre usuario afectado:<PERSON>uario de red del afectado:Maripoda\nContacto usuario afectado:**********\nDescripción del error:<PERSON><PERSON>, su ayuda por favor, algunas personas están intentando ingresar a la consola y modulo dashboard y les aparece el siguiente error \"No se puede acceder a este sitio web\", algunas de las personas afectadas son: carlqutr\nclaujalp \nluismapr\nand<PERSON><PERSON><PERSON>\n", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: SERVICIO RESTABLECIDO SIN ACCION\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: ADMINISTRACION BMC\\nClosure Product Category Tier2: Incidente\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Mensaje Error/Falla\\nDWP_SRID: 94520\\nDWP_SRInstanceID: AGGJYG8IF3BYEASZBP10SZBP10HZGE\\nIncident Number: INC000000074272\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Plataforma ITSM BMC\\nProduct Categorization Tier 2: Plataforma ITSM BMC\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "**********;'maripoda';", "last_modified_date": "2025-07-15T07:00:41.000+0000", "create_date": "2025-07-07T20:12:41.000+0000", "priority": "Medium", "impact": "3-Moderate/Limited", "urgency": "3-Medium", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): solo les ocurrió a usuarios con proveedor de internet movistar\nSolución: Operatividad recuperada\nCausa Raíz (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): Si\n\nBMC Helix, como solución basada en la nube, requiere una conexión a internet estable para funcionar correctamente. Si la conexión a internet del usuario es intermitente o se corta, el acceso a la plataforma se verá afectado.", "resolution_method": null, "dwp_number": "94520", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nCausa (Motivo, origen o razón de que suceda el incidente): solo les ocurrió a usuarios con proveedor de internet movistar\nSolución: Operatividad recuperada\nCausa Raíz (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): Si\n\nBMC Helix, como solución basada en la nube, requiere una conexión a internet estable para funcionar correctamente. Si la conexión a internet del usuario es intermitente o se corta, el acceso a la plataforma se verá afectado.\n\nCategoría de resolución:\nSERVICIOS DE TECNOLOGIA.ADMINISTRACION BMC.SERVICIO RESTABLECIDO SIN ACCION", "last_modified_date": "2025-07-09T14:26:25.000+0000"}}, {"incident_id": "INC000000076992", "status": "Closed", "description": "Soporte BMC Helix", "detailed_description": "Aplicativo Afectado:Errores BMC Helix\nTipo de falla:Otro\nNombre usuario afectado:<PERSON>\nUsuario de red del afectado:Maripoda\nContacto usuario afectado:**********\nDescripción del error:Prueba - Omitir\n", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-01FAEBE6892011EF88CD5E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: USUARIOS\\nClosure Product Category Tier1: Aplicaciones\\nResolution Category Tier 2: PROCESO\\nClosure Product Category Tier2: Aplicaciones de negocio\\nHPD_CI: Cotizador Hogar\\nClosure Product Category Tier3: Aplicaciones de negocio\\nDWP_SRID: 97791\\nDWP_SRInstanceID: AGGHE4WHFZM13ASZGY84SZGY84Y3X4\\nIncident Number: INC000000076992\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: API Cotizador\\nCategorization Tier 3: Bloqueos-Lentitudes\\nProduct Categorization Tier 1: Coti<PERSON>or Hogar\\nProduct Categorization Tier 3: Hogares Sura\\nProduct Categorization Tier 2: Propiedad\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "**********;1000000422;'maripoda';", "last_modified_date": "2025-07-16T07:03:20.000+0000", "create_date": "2025-07-10T16:16:32.000+0000", "priority": "Medium", "impact": "3-Moderate/Limited", "urgency": "3-Medium", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Se recupera la informacion", "resolution_method": null, "dwp_number": "97791", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nSe recupera la informacion\n\nCategoría de resolución:\nCAPACITACION.PROCESO.USUARIOS", "last_modified_date": "2025-07-10T17:09:16.000+0000"}}, {"incident_id": "INC000000002266", "status": "Closed", "description": "prueba para asociar", "detailed_description": "prueba para asociar", "technical_info": "Associated Alarm: None\\nDirect Contact City: MEDELLIN\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Home Cotizador\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Home Cotizador\\nClosure Product Category Tier3: Home Cotizador\\nDWP_SRID: 2616\\nIncident Number: INC000000002266\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: Cotizador\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: Home Cotizador\\nProduct Categorization Tier 3: Home Cotizador\\nProduct Categorization Tier 2: Home Cotizador\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000416;1000000478;'maripoda';'luisgupv';", "last_modified_date": "2025-02-16T01:29:50.000+0000", "create_date": "2024-10-23T16:04:42.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Prueba", "resolution_method": null, "dwp_number": "2616", "found_by": "Direct Contact", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000015309", "status": "Cancelled", "description": "INCIDENTE DE PRUEBAS", "detailed_description": "Aplicativo o equipo afectado: \nDescripción del error o falla: INCIDENTE DE PRUEBAS\nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nDirect Contact City: MEDELLIN\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nIncident Number: INC000000015309\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000124;1000000478;'maripoda';", "last_modified_date": "2025-04-22T20:49:28.000+0000", "create_date": "2025-03-17T13:45:31.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "INC000000015309", "found_by": "Direct Contact", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: pruebas no atender", "last_modified_date": "2025-03-17T14:17:55.000+0000"}}, {"incident_id": "INC000000015311", "status": "Cancelled", "description": "INCIDENTE DE PRUEBAS", "detailed_description": "Aplicativo o equipo afectado: \nDescripción del error o falla:  INCIDENTE DE PRUEBAS\nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nDirect Contact City: MEDELLIN\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nIncident Number: INC000000015311\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000124;1000000478;'maripoda';", "last_modified_date": "2025-04-22T20:49:28.000+0000", "create_date": "2025-03-17T13:47:41.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "INC000000015311", "found_by": "Direct Contact", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: pruebas no atender", "last_modified_date": "2025-03-17T14:19:48.000+0000"}}, {"incident_id": "INC000000016368", "status": "Cancelled", "description": "edge.exception.ExecuteRoutineException: \"Error tarifando la cobertura de \"Responsabilidad Civil\"", "detailed_description": "Aplicativo o equipo afectado: prueba no atender\nDescripción del error o falla: \nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nDirect Contact City: MEDELLIN\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nIncident Number: INC000000016368\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000076;1000000478;'maripoda';", "last_modified_date": "2025-04-22T20:49:28.000+0000", "create_date": "2025-03-18T21:39:05.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "INC000000016368", "found_by": "Direct Contact", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: pruebas no atender", "last_modified_date": "2025-03-18T21:40:17.000+0000"}}, {"incident_id": "INC000000032662", "status": "Closed", "description": "Plantilla General", "detailed_description": "Aplicativo o equipo afectado: \nDescripción del error o falla: \nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-027F8D10892011EF894A5E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nDirect Contact City: MEDELLIN\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Policy Center\\nClosure Product Category Tier3: Pruebas\\nDWP_SRID: 41830\\nIncident Number: INC000000032662\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000126;'maripoda';", "last_modified_date": "2025-04-29T07:02:32.000+0000", "create_date": "2025-04-23T17:15:09.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Se cierra incidente por pruebas en la herramienta", "resolution_method": null, "dwp_number": "41830", "found_by": "Direct Contact", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nSe cierra incidente por pruebas en la herramienta\n\nCategoría de resolución:\nGestión BMC.Pruebas PDN.Consola", "last_modified_date": "2025-04-23T19:06:26.000+0000"}}, {"incident_id": "INC000000063006", "status": "Cancelled", "description": "Plantilla General", "detailed_description": "Aplicativo o equipo afectado: \nDescripción del error o falla: \nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_DWPCPassword: ***\\nDWP_SRID: 80369\\nIncident Number: INC000000063006\\nCity: MEDELLIN\\nCategorization Tier 1: Gestor de procesos\\nCategorization Tier 2: Salud Voluntarios\\nCategorization Tier 3: Salud\\nService Type: User Service Restoration\\nAssociated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Aplicaciones\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Aplicaciones de negocio\\nHPD_CI: SALUDARL\\nClosure Product Category Tier3: Aplicaciones de negocio\\nz1D_SV_ServiceType: User Service Restoration\\nHPD_CI_ReconID: OI-02FE294A892011EF89B95E0313B2FF74\\nMajor Incident: No\\nCriticidadCI: PRIORITY_5\\nsel_Criticidad: NoCritica\\nHPD_CI_FormName: BMC_APPLICATION\\nRequestCreatedFromDWP: No\\nProduct Categorization Tier 1: ADR - Autorizaciones de Salud por la Web\\nProduct Categorization Tier 3: ADR Salud\\nProduct Categorization Tier 2: ADR - Autorizaciones de Salud por la Web\\nz1D_CI_FormName: AST:Application\\nDirect Contact City: MEDELLIN", "assigned_to": null, "assignee_groups": "1000000483;1000000478;'marirtag';'maripoda';", "last_modified_date": "2025-06-17T16:32:26.000+0000", "create_date": "2025-06-17T16:29:26.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO):", "resolution_method": null, "dwp_number": "80369", "found_by": "Assignee Groups", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: pruebas", "last_modified_date": "2025-06-17T16:32:25.000+0000"}}, {"incident_id": "INC000000050822", "status": "Cancelled", "description": "Plantilla General", "detailed_description": "Aplicativo o equipo afectado: \nDescripción del error o falla: \nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_DWPCPassword: ***\\nDWP_SRID: 64391\\nIncident Number: INC000000050822\\nCity: MEDELLIN\\nCategorization Tier 1: APLICACIONES IPS\\nCategorization Tier 2: Plataforma Domiciliaria Salud en Casa\\nCategorization Tier 3: Errores Aplicacion\\nService Type: User Service Restoration\\nAssociated Alarm: None\\nResolution Category Tier 3: NO CORRESPONDE A UN INCIDENTE\\nClosure Product Category Tier1: Plataforma de Canales Masivos\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Plataforma de Canales Masivos\\nHPD_CI: Salud Familiar\\nClosure Product Category Tier3: Plataforma de Canales Masivos\\nz1D_SV_ServiceType: User Service Restoration\\nHPD_CI_ReconID: OI-01F487C4892011EF88C95E0313B2FF74\\nMajor Incident: No\\nCriticidadCI: PRIORITY_5\\nsel_Criticidad: NoCritica\\nHPD_CI_FormName: BMC_APPLICATION\\nRequestCreatedFromDWP: No\\nProduct Categorization Tier 1: Plataforma de Canales Masivos\\nProduct Categorization Tier 3: Plataforma de Canales Masivos\\nProduct Categorization Tier 2: Plataforma de Canales Masivos\\nz1D_CI_FormName: AST:Application\\nDirect Contact City: MEDELLIN", "assigned_to": null, "assignee_groups": "1000000483;1000000478;'mariancg';'maripoda';", "last_modified_date": "2025-05-26T14:50:53.000+0000", "create_date": "2025-05-26T14:48:10.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO):", "resolution_method": null, "dwp_number": "64391", "found_by": "Assignee Groups", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Pruebas de nuevo motivo de cancelación", "last_modified_date": "2025-05-26T14:50:52.000+0000"}}, {"incident_id": "INC000000042459", "status": "Cancelled", "description": "Petición Hija relacionada a la afectación mayor 53382", "detailed_description": "Aplicativo afectado: Agendaweb\nRuta por la que ingresa: Prueba\nOperación que estaba realizando: Prueba\nNombre del afectado: Maripoda\nTeléfono del afectado: 123456\nUsuario con el que ingresó a la aplicación: Prueba\nDescripción del error o falla: Prueba", "technical_info": "SpecialHandling_Sura: Tercero EPS\\nz1D_DWPCPassword: ***\\nDWP_SRID: 53795\\nIncident Number: INC000000042459\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nService Type: User Service Restoration\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nz1D_SV_ServiceType: User Service Restoration\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nMajor Incident: No\\nCriticidadCI: PRIORITY_5\\nHPD_CI_FormName: BMC_APPLICATION\\nRequestCreatedFromDWP: No\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nz1D_CI_FormName: AST:Application", "assigned_to": null, "assignee_groups": "1000000478;'maripoda';", "last_modified_date": "2025-05-12T18:30:18.000+0000", "create_date": "2025-05-12T18:29:46.000+0000", "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "53795", "found_by": "Assignee Groups", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Prueba", "last_modified_date": "2025-05-12T18:30:18.000+0000"}}, {"incident_id": "INC000000015525", "status": "Cancelled", "description": "Plantilla General", "detailed_description": "Aplicativo o equipo afectado: \nDescripción del error o falla: Pruebas no atender 2\nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_DWPCPassword: ***\\nDWP_SRID: 19160\\nIncident Number: INC000000015525\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nService Type: User Service Restoration\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nz1D_SV_ServiceType: User Service Restoration\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nMajor Incident: No\\nHPD_CI_FormName: BMC_APPLICATION\\nRequestCreatedFromDWP: No\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nz1D_CI_FormName: AST:Application", "assigned_to": null, "assignee_groups": "1000000076;1000000478;'maripoda';", "last_modified_date": "2025-06-17T16:47:15.000+0000", "create_date": "2025-03-17T16:37:04.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "19160", "found_by": "Assignee Groups", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Prueba", "last_modified_date": "2025-06-17T16:47:15.000+0000"}}, {"incident_id": "INC000000015517", "status": "Cancelled", "description": "Plantilla General", "detailed_description": "Prueba inc relacionada con INC000000015276\nAplicativo o equipo afectado: \nDescripción del error o falla: \nRuta por la que ingresa: \nOperación que estaba realizando: \nNombre del afectado: \nUbicación (Sede y piso): \nTeléfonos (Celular, fijo, ext.): \nNúmero de usuarios afectados: \nUsuario con el que ingresó a la aplicación (si son varios, agregue algunos):", "technical_info": "z1D_DWPCPassword: ***\\nDWP_SRID: 19070\\nIncident Number: INC000000015517\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Pruebas\\nService Type: User Service Restoration\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Pruebas\\nHPD_CI: Plataforma ITSM BMC\\nClosure Product Category Tier3: Pruebas\\nz1D_SV_ServiceType: User Service Restoration\\nHPD_CI_ReconID: OI-A779D334001711F0B82D3AB36C828F5E\\nMajor Incident: No\\nHPD_CI_FormName: BMC_APPLICATION\\nRequestCreatedFromDWP: No\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Pruebas\\nProduct Categorization Tier 2: Pruebas\\nz1D_CI_FormName: AST:Application", "assigned_to": null, "assignee_groups": "1000000478;'maripoda';", "last_modified_date": "2025-06-17T16:51:31.000+0000", "create_date": "2025-03-17T16:18:48.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "19070", "found_by": "Assignee Groups", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Prueba", "last_modified_date": "2025-06-17T16:51:31.000+0000"}}, {"incident_id": "INC000000003604", "status": "Closed", "description": "Cotizador Autos - Error en la aplicación", "detailed_description": "Pruebas de problemas", "technical_info": "SpecialHandling_Sura: Canal Asesor - Red Comercial\\nz1D_DWPCPassword: ***\\nDWP_SRID: 4278\\nIncident Number: INC000000003604\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: Cotizador\\nCategorization Tier 3: Errores Aplicacion\\nMajor Incident Proposed Date: 2025-01-24T15:25:45.000+0000\\nMajor Incident Accepted Date: 2025-01-24T15:25:45.000+0000\\nService Type: User Service Restoration\\nAssociated Alarm: None\\nResolution Category Tier 3: Consola\\nClosure Product Category Tier1: Group Center\\nResolution Category Tier 2: Pruebas PDN\\nClosure Product Category Tier2: Empresarial\\nHPD_CI: Cotizador de Autos\\nClosure Product Category Tier3: Empresarial\\nz1D_SV_ServiceType: User Service Restoration\\nHPD_CI_ReconID: OI-031F7424892011EF89D95E0313B2FF74\\nMajor Incident: Yes\\nsel_Criticidad: Critica\\nchr_CIRelacionado: Cotizador de Autos\\nrad_planRecuperacion: Si\\nHPD_CI_FormName: BMC_APPLICATION\\nRequestCreatedFromDWP: No\\nProduct Categorization Tier 1: Group Center\\nProduct Categorization Tier 3: Empresarial\\nProduct Categorization Tier 2: Empresarial\\nz1D_CI_FormName: AST:Application\\nDirect Contact City: MEDELLIN", "assigned_to": null, "assignee_groups": "1000000076;1000000483;1000000478;'luisgupv';'maripoda';", "last_modified_date": "2025-02-16T01:49:39.000+0000", "create_date": "2025-01-20T14:43:43.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): Prueba\n\nSolución: Incidente resuelto por pruebas\n\nCausa Raíz (Identificada/Sin identificar): Prueba\n\nConfirmar operatividad del usuario Afectado (SI/NO): Prueba", "resolution_method": null, "dwp_number": "4278", "found_by": "Assignee Groups", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nCausa (Motivo, origen o razón de que suceda el incidente): Prueba\n\nSolución: Incidente resuelto por pruebas\n\nCausa Raíz (Identificada/Sin identificar): Prueba\n\nConfirmar operatividad del usuario Afectado (SI/NO): Prueba\n\nCategoría de resolución:\nGestión BMC.Pruebas PDN.Consola", "last_modified_date": "2025-01-29T21:03:16.000+0000"}}, {"incident_id": "INC000000001810", "status": "Cancelled", "description": "Cotizador- Empresarial", "detailed_description": "Aplicativo Afectado: CotizadorEmpresarial\nTipo de falla: Transacción en proceso\nProducto / Proceso: Nuevo\nProducto: Embarcaciones de recreo\nUsuario afectado: Maripoda\nCelular del afectado: 123456789\nRuta por la que ingresa: No Aplica\nCotización: 123456795678\nPoliza: \nRadicado y/o poliza: 123456790\n¿Su inconveniente es con el menú del cotizador o con el ingreso al cotizador?: No\n¿La póliza tiene oneroso?: No\nDescripción del error: Hola, su ayuda por favor con el siguiente error que se muestra en el cotizador para la cotización 537950945, gracias. Adjunto error.\n¿Cuántas personas hay afectadas?: 1 persona¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?:", "technical_info": "SpecialHandling_Sura: Canal Asesor - Red Comercial\\nz1D_DWPCPassword: ***\\nDWP_SRID: 2022\\nIncident Number: INC000000001810\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC (Pruebas)\\nCategorization Tier 3: Falla\\nService Type: User Service Restoration\\nAssociated Alarm: None\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nMajor Incident: No\\nRequestCreatedFromDWP: No\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente", "assigned_to": null, "assignee_groups": "1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-10-07T20:32:37.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "2022", "found_by": "Assignee Groups", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000001189", "status": "Closed", "description": "Group Center Empresarial", "detailed_description": "Aplicativo Afectado: GroupCenterEmpresarial\nTipo de falla: Transacción en proceso\nRuta por la que ingresa: PRUEBA\nProducto o solución afectada: PRUEBA\nPóliza: 12345\nOperación: PRUEBA\nUsuario afectado: PRUEBA\nContacto del usuario: 12345\nNombre de la oficina: PRUEBA\nCotización: 12345\n¿La póliza tiene oneroso?: Si\nDescripción del error: PRUEBA\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso PQRS: \n¿Cuál es el numero de la PQRS?:", "technical_info": "SpecialHandling_Sura: Canal Asesor - Red Comercial\\nz1D_DWPCPassword: ***\\nDWP_SRID: 1543\\nIncident Number: INC000000001189\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nService Type: User Service Restoration\\nAssociated Alarm: None\\nResolution Category Tier 3: MEMORIA\\nClosure Product Category Tier1: AUS - Acceso Unico de Solicitudes\\nResolution Category Tier 2: AUMENTO\\nClosure Product Category Tier2: AUS - Acceso Unico de Solicitudes\\nClosure Product Category Tier3: AUS - Acceso Unico de Solicitudes\\nz1D_SV_ServiceType: User Service Restoration\\nMajor Incident: No\\nchr_justificacionPendiente: PRUEBA\\nRequestCreatedFromDWP: No\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente", "assigned_to": null, "assignee_groups": "1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:13:58.000+0000", "create_date": "2024-09-24T21:49:55.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Prueba para reapertura y cumplimiento de OLA", "resolution_method": null, "dwp_number": "1543", "found_by": "Assignee Groups", "last_note": {"description": "Sin notas", "detailed_description": "No hay notas disponibles para este incidente"}}, {"incident_id": "INC000000000574", "status": "Cancelled", "description": "<PERSON><PERSON><PERSON>", "detailed_description": "Aplicativo Afectado: Vivemas\nProducto / Proceso: Vive Mas - Salud Financiera\nDocumento del cliente afectado: 12345\nDescripción del error: PRUEBA - OMITIR\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: No\nMedio de Ingreso: \n¿Cuál es el numero de la PQRS?:", "technical_info": "SpecialHandling_Sura: Canal Asesor - Red Comercial\\nz1D_DWPCPassword: ***\\nDWP_SRID: 668\\nIncident Number: INC000000000574\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Plataforma ITSM BMC\\nCategorization Tier 3: Falla\\nService Type: User Service Restoration\\nAssociated Alarm: None\\nResolution Category Tier 3: SOLICITUD DE CLIENTE/USUARIO\\nClosure Product Category Tier1: Plataforma ITSM BMC\\nResolution Category Tier 2: CANCELAR\\nClosure Product Category Tier2: Incidente\\nClosure Product Category Tier3: Error en la aplicación\\nz1D_SV_ServiceType: User Service Restoration\\nMajor Incident: No\\nRequestCreatedFromDWP: No\\nProduct Categorization Tier 1: Plataforma ITSM BMC\\nProduct Categorization Tier 3: Error en la aplicación\\nProduct Categorization Tier 2: Incidente", "assigned_to": null, "assignee_groups": "1000000478;'maripoda';", "last_modified_date": "2025-02-16T01:10:25.000+0000", "create_date": "2024-09-09T14:11:34.000+0000", "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "No especificado", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "668", "found_by": "Assignee Groups", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "PRUEBA Reactivación Automática", "last_modified_date": "2024-09-09T14:18:59.000+0000"}}]}}