#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script AGI simplificado para Asterisk - Compatible con Python 2.7+
Integración con BMC Remedy sin dependencias complejas

Uso desde Asterisk:
same => n,AGI(mfa_simple.py, "create", "${document}", "${ticketDetail}", "${cellphone}", "${STRREPLACE(UNIQUEID,".","")}", "${destinatario}", "${limite}")

Parámetros:
- action: "create" (acción a realizar)
- document: Número de cédula del usuario
- ticketDetail: Detalle del ticket (informativo)
- cellphone: Número de teléfono (informativo)
- uniqueId: ID único de la llamada
- destinatario: Email del destinatario (opcional)
- limite: Número máximo de casos a incluir (opcional, por defecto 5)

Ejemplos:
same => n,AGI(mfa_simple.py, "create", "1152213619", "Consulta telefónica", "3001234567", "123456789", "<EMAIL>", "10")
same => n,AGI(mfa_simple.py, "create", "1152213619", "Consulta telefónica", "3001234567", "123456789", "<EMAIL>")
"""

import sys
import os
import subprocess
import time
from datetime import datetime

class SimpleAGIHandler:
    """Manejador AGI simplificado sin dependencias externas"""
    
    def __init__(self):
        self.agi_env = {}
        self.setup_logging()
        self.read_agi_environment()
    
    def setup_logging(self):
        """Configurar logging simple"""
        # Crear directorio de logs si no existe
        log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
        if not os.path.exists(log_dir):
            try:
                os.makedirs(log_dir)
            except OSError:
                pass
        
        self.log_file = os.path.join(log_dir, "agi_simple.log")
    
    def log(self, level, message):
        """Logging simple a archivo"""
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            log_entry = "{} - AGI_SIMPLE - {} - {}\n".format(timestamp, level, message)
            
            with open(self.log_file, 'a') as f:
                f.write(log_entry)
        except Exception:
            pass  # Fallar silenciosamente si no se puede escribir log
    
    def read_agi_environment(self):
        """Leer variables de entorno de AGI"""
        try:
            # Solo leer si no estamos en un terminal
            if hasattr(sys.stdin, 'isatty') and not sys.stdin.isatty():
                while True:
                    line = sys.stdin.readline().strip()
                    if not line:
                        break
                    
                    if ':' in line:
                        key, value = line.split(':', 1)
                        self.agi_env[key.strip()] = value.strip()
                
                self.log("INFO", "AGI Environment: {}".format(self.agi_env))
            else:
                self.log("INFO", "Ejecutándose en modo terminal (no AGI)")
        except Exception as e:
            self.log("ERROR", "Error reading AGI environment: {}".format(str(e)))
    
    def agi_response(self, message):
        """Enviar respuesta a Asterisk"""
        print(message)
        sys.stdout.flush()
        self.log("INFO", "AGI Response: {}".format(message))
    
    def set_variable(self, variable, value):
        """Establecer variable en Asterisk"""
        self.agi_response('SET VARIABLE {} "{}"'.format(variable, value))
    
    def verbose(self, message, level=1):
        """Enviar mensaje verbose a Asterisk"""
        self.agi_response('VERBOSE "{}" {}'.format(message, level))
    
    def create_ticket_report(self, document, ticket_detail, cellphone, unique_id, destinatario=None, limite=5):
        """
        Crear reporte de tickets para el documento especificado
        
        Args:
            document: Número de cédula
            ticket_detail: Detalle del ticket
            cellphone: Teléfono celular
            unique_id: ID único de la llamada
            destinatario: Email del destinatario (opcional)
            limite: Número máximo de casos (opcional, por defecto 5)
        """
        try:
            self.log("INFO", "Iniciando búsqueda para documento: {}".format(document))
            self.verbose("Buscando tickets para documento: {}".format(document))
            
            # Construir comando para ejecutar el script principal
            project_root = os.path.dirname(os.path.abspath(__file__))
            
            # Detectar versión de Python disponible
            python_cmd = self.detect_python_version()
            
            cmd = [
                python_cmd, 'main.py',
                document,
                '--limite', str(limite)
            ]
            
            if destinatario:
                cmd.extend(['--destinatario', destinatario])
            
            self.log("INFO", "Ejecutando comando: {}".format(' '.join(cmd)))
            
            # Ejecutar comando
            try:
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    cwd=project_root
                )
                
                stdout, stderr = process.communicate()
                
                # Convertir bytes a string si es necesario (Python 3)
                if hasattr(stdout, 'decode'):
                    stdout = stdout.decode('utf-8')
                if hasattr(stderr, 'decode'):
                    stderr = stderr.decode('utf-8')
                
                self.log("INFO", "Código de salida: {}".format(process.returncode))
                if stdout:
                    self.log("INFO", "STDOUT: {}".format(stdout))
                if stderr:
                    self.log("ERROR", "STDERR: {}".format(stderr))
                
                resultado = process.returncode == 0
                
            except Exception as e:
                self.log("ERROR", "Error ejecutando subprocess: {}".format(str(e)))
                resultado = False
            
            if resultado:
                self.set_variable("TICKET_SEARCH_RESULT", "SUCCESS")
                self.set_variable("TICKET_SEARCH_MESSAGE", "Reporte generado exitosamente para {}".format(document))
                self.verbose("Reporte generado exitosamente para documento {}".format(document))
                self.log("INFO", "Reporte generado exitosamente para documento: {}".format(document))
                return True
            else:
                self.set_variable("TICKET_SEARCH_RESULT", "FAILED")
                self.set_variable("TICKET_SEARCH_MESSAGE", "Error al generar reporte para {}".format(document))
                self.verbose("Error al generar reporte para documento {}".format(document))
                self.log("ERROR", "Error al generar reporte para documento: {}".format(document))
                return False
                
        except Exception as e:
            error_msg = "Error en create_ticket_report: {}".format(str(e))
            self.log("ERROR", error_msg)
            print("ERROR: {}".format(error_msg), file=sys.stderr)
            self.set_variable("TICKET_SEARCH_RESULT", "ERROR")
            self.set_variable("TICKET_SEARCH_MESSAGE", error_msg)
            self.verbose("Error: {}".format(error_msg))
            return False
    
    def detect_python_version(self):
        """Detectar la mejor versión de Python disponible"""
        python_versions = ['python3.11', 'python3.10', 'python3.9', 'python3.8', 'python3.7', 'python3', 'python']
        
        for python_cmd in python_versions:
            try:
                # Probar si el comando existe
                with open(os.devnull, 'w') as devnull:
                    subprocess.call([python_cmd, '--version'], stdout=devnull, stderr=devnull)
                self.log("INFO", "Usando Python: {}".format(python_cmd))
                return python_cmd
            except (OSError, subprocess.CalledProcessError):
                continue
        
        # Fallback a python por defecto
        self.log("WARNING", "No se pudo detectar versión de Python, usando 'python'")
        return 'python'
    
    def handle_command(self, action, *args):
        """Manejar comando AGI"""
        try:
            self.log("INFO", "Comando recibido: {} con argumentos: {}".format(action, args))
            
            if action == "create":
                if len(args) >= 4:
                    document, ticket_detail, cellphone, unique_id = args[:4]
                    # Verificar argumentos opcionales
                    destinatario = args[4] if len(args) > 4 else None
                    limite = 5  # Valor por defecto
                    
                    if len(args) > 5 and args[5].isdigit():
                        limite = int(args[5])
                    
                    self.log("INFO", "Parámetros: document={}, destinatario={}, limite={}".format(
                        document, destinatario, limite))
                    
                    return self.create_ticket_report(document, ticket_detail, cellphone, unique_id, destinatario, limite)
                else:
                    error_msg = "Argumentos insuficientes para 'create'. Recibidos: {}, esperados: 4 (destinatario y límite opcionales)".format(len(args))
                    self.log("ERROR", error_msg)
                    self.set_variable("TICKET_SEARCH_RESULT", "ERROR")
                    self.set_variable("TICKET_SEARCH_MESSAGE", error_msg)
                    return False
            else:
                error_msg = "Acción no reconocida: {}".format(action)
                self.log("ERROR", error_msg)
                self.set_variable("TICKET_SEARCH_RESULT", "ERROR")
                self.set_variable("TICKET_SEARCH_MESSAGE", error_msg)
                return False
                
        except Exception as e:
            error_msg = "Error manejando comando: {}".format(str(e))
            self.log("ERROR", error_msg)
            self.set_variable("TICKET_SEARCH_RESULT", "ERROR")
            self.set_variable("TICKET_SEARCH_MESSAGE", error_msg)
            return False

def main():
    """Función principal del script AGI"""
    try:
        # Crear manejador AGI
        agi = SimpleAGIHandler()
        
        # Obtener argumentos de la línea de comandos
        if len(sys.argv) < 2:
            agi.log("ERROR", "No se proporcionó acción")
            agi.set_variable("TICKET_SEARCH_RESULT", "ERROR")
            agi.set_variable("TICKET_SEARCH_MESSAGE", "No se proporcionó acción")
            sys.exit(1)
        
        action = sys.argv[1]
        args = sys.argv[2:] if len(sys.argv) > 2 else []
        
        # Ejecutar comando
        success = agi.handle_command(action, *args)
        
        # Salir con código apropiado
        sys.exit(0 if success else 1)
        
    except Exception as e:
        error_msg = "Error fatal en main: {}".format(str(e))
        print('SET VARIABLE TICKET_SEARCH_RESULT "FATAL_ERROR"')
        print('SET VARIABLE TICKET_SEARCH_MESSAGE "Error fatal: {}"'.format(str(e)))
        sys.exit(1)

if __name__ == "__main__":
    main()
