#!/bin/bash

# Script de instalación para BMC Remedy con Docker Python 3.6
# Para uso con Asterisk AGI

set -e

echo "=== INSTALACIÓN BMC REMEDY DOCKER PYTHON 3.6 ==="
echo "Este script instalará Docker y construirá la imagen para BMC Remedy"
echo

# Verificar si se ejecuta como root o con sudo
if [[ $EUID -eq 0 ]]; then
   echo "⚠️  No ejecutar este script como root directamente"
   echo "   Usar: ./install_docker_py36.sh"
   exit 1
fi

# Verificar si sudo está disponible
if ! command -v sudo &> /dev/null; then
    echo "❌ sudo no está disponible. Instalar sudo primero."
    exit 1
fi

echo "🔍 Verificando sistema..."

# Detectar distribución
if [ -f /etc/os-release ]; then
    . /etc/os-release
    OS=$NAME
    VER=$VERSION_ID
else
    echo "❌ No se pudo detectar la distribución del sistema"
    exit 1
fi

echo "📋 Sistema detectado: $OS $VER"

# Función para instalar Docker en Ubuntu/Debian
install_docker_ubuntu() {
    echo "📦 Instalando Docker en Ubuntu/Debian..."
    
    # Actualizar paquetes
    sudo apt update
    
    # Instalar dependencias
    sudo apt install -y apt-transport-https ca-certificates curl gnupg lsb-release
    
    # Agregar clave GPG de Docker
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    
    # Agregar repositorio
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # Instalar Docker
    sudo apt update
    sudo apt install -y docker-ce docker-ce-cli containerd.io docker-compose
    
    # Agregar usuario al grupo docker
    sudo usermod -aG docker $USER
    
    # Iniciar y habilitar Docker
    sudo systemctl start docker
    sudo systemctl enable docker
    
    echo "✅ Docker instalado correctamente"
}

# Función para instalar Docker en CentOS/RHEL
install_docker_centos() {
    echo "📦 Instalando Docker en CentOS/RHEL..."
    
    # Instalar dependencias
    sudo yum install -y yum-utils
    
    # Agregar repositorio
    sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
    
    # Instalar Docker
    sudo yum install -y docker-ce docker-ce-cli containerd.io docker-compose
    
    # Agregar usuario al grupo docker
    sudo usermod -aG docker $USER
    
    # Iniciar y habilitar Docker
    sudo systemctl start docker
    sudo systemctl enable docker
    
    echo "✅ Docker instalado correctamente"
}

# Verificar si Docker ya está instalado
if command -v docker &> /dev/null; then
    echo "✅ Docker ya está instalado"
    docker --version
else
    echo "📦 Docker no encontrado, instalando..."
    
    case "$OS" in
        "Ubuntu"*|"Debian"*)
            install_docker_ubuntu
            ;;
        "CentOS"*|"Red Hat"*)
            install_docker_centos
            ;;
        *)
            echo "❌ Distribución no soportada: $OS"
            echo "   Instalar Docker manualmente y ejecutar este script nuevamente"
            exit 1
            ;;
    esac
fi

echo
echo "🔨 Construyendo imagen Docker con Python 3.6..."

# Verificar que los archivos necesarios existen
if [ ! -f "Dockerfile.py36" ]; then
    echo "❌ Archivo Dockerfile.py36 no encontrado"
    exit 1
fi

if [ ! -f "requirements-py36.txt" ]; then
    echo "❌ Archivo requirements-py36.txt no encontrado"
    exit 1
fi

# Construir la imagen
sudo docker build -f Dockerfile.py36 -t bmc-remedy-py36 .

echo
echo "🧪 Probando la imagen..."

# Verificar Python 3.6
echo "📋 Verificando versión de Python:"
sudo docker run --rm bmc-remedy-py36 python --version

# Probar la aplicación
echo "📋 Probando aplicación:"
sudo docker run --rm bmc-remedy-py36 python main.py --help

echo
echo "✅ INSTALACIÓN COMPLETADA"
echo
echo "📋 PRÓXIMOS PASOS:"
echo "1. Cerrar sesión y volver a iniciar para aplicar permisos de Docker"
echo "2. Probar la aplicación:"
echo "   sudo docker run --rm -v \$(pwd)/logs:/app/logs bmc-remedy-py36 python main.py 1152213619 --destinatario <EMAIL>"
echo
echo "3. Para integrar con Asterisk AGI:"
echo "   - Copiar el script AGI a /var/lib/asterisk/agi-bin/"
echo "   - Configurar extensions.conf"
echo "   - Dar permisos de Docker al usuario asterisk"
echo
echo "📖 Ver README_DOCKER_PY36.md para más detalles"
echo

# Crear script de prueba rápida
cat > test_installation.sh << 'EOF'
#!/bin/bash
echo "=== PRUEBA RÁPIDA DE INSTALACIÓN ==="
echo "Probando imagen Docker con Python 3.6..."

sudo docker run --rm \
  -v $(pwd)/logs:/app/logs \
  bmc-remedy-py36 \
  python main.py 1152213619 --destinatario <EMAIL> --limite 3

echo "✅ Prueba completada"
EOF

chmod +x test_installation.sh

echo "🚀 Script de prueba creado: ./test_installation.sh"
echo "   Ejecutar después de reiniciar sesión"
