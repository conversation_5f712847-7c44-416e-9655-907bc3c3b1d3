#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de prueba para verificar que los archivos MFA funcionan con el comando 'search'
"""

import subprocess
import sys
import time

def test_mfa_search():
    """Probar el comando search en los archivos MFA"""
    
    print("=" * 70)
    print("🧪 PRUEBA DE COMANDO 'search' EN ARCHIVOS MFA")
    print("=" * 70)
    print()
    
    # Parámetros de prueba
    cedula = "1152213619"
    descripcion = "Prueba desde script"
    telefono = "3001234567"
    unique_id = "test123456"
    destinatario = "<EMAIL>"
    limite = "3"
    
    print("📋 PARÁMETROS DE PRUEBA:")
    print(f"- Cédula: {cedula}")
    print(f"- Descripción: {descripcion}")
    print(f"- Teléfono: {telefono}")
    print(f"- ID único: {unique_id}")
    print(f"- Destinatario: {destinatario}")
    print(f"- Límite: {limite}")
    print()
    
    # Lista de archivos MFA a probar
    mfa_files = [
        ("mfa.py", "python3"),
        ("mfa_simple.py", "python3"),
        ("mfa_auto.py", "python3")
    ]
    
    resultados = []
    
    for archivo, python_cmd in mfa_files:
        print(f"🔄 Probando {archivo}...")
        print("-" * 50)
        
        try:
            start_time = time.time()
            
            cmd = [
                python_cmd, archivo,
                "search",  # Comando corregido
                cedula,
                descripcion,
                telefono,
                unique_id,
                destinatario,
                limite
            ]
            
            print(f"Comando: {' '.join(cmd)}")
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            print(f"⏱️  Tiempo: {execution_time:.1f}s")
            print(f"🔄 Código: {result.returncode}")
            
            if result.stdout:
                print("📤 STDOUT:")
                print(result.stdout[:500] + "..." if len(result.stdout) > 500 else result.stdout)
            
            if result.stderr:
                print("⚠️  STDERR:")
                print(result.stderr[:500] + "..." if len(result.stderr) > 500 else result.stderr)
            
            # Evaluar resultado
            exito = result.returncode == 0
            if exito:
                print("✅ ÉXITO")
            else:
                print("❌ FALLO")
            
            resultados.append({
                'archivo': archivo,
                'exito': exito,
                'tiempo': execution_time,
                'codigo': result.returncode
            })
            
        except subprocess.TimeoutExpired:
            print("❌ TIMEOUT")
            resultados.append({
                'archivo': archivo,
                'exito': False,
                'tiempo': 60.0,
                'codigo': -1
            })
        except Exception as e:
            print(f"❌ ERROR: {str(e)}")
            resultados.append({
                'archivo': archivo,
                'exito': False,
                'tiempo': 0.0,
                'codigo': -2
            })
        
        print()
    
    # Resumen final
    print("=" * 70)
    print("📊 RESUMEN DE RESULTADOS")
    print("=" * 70)
    
    exitos = 0
    for resultado in resultados:
        archivo = resultado['archivo']
        exito = resultado['exito']
        tiempo = resultado['tiempo']
        codigo = resultado['codigo']
        
        status = "✅ ÉXITO" if exito else "❌ FALLO"
        print(f"{archivo:15} | {status:10} | {tiempo:6.1f}s | Código: {codigo}")
        
        if exito:
            exitos += 1
    
    print()
    print(f"📈 ESTADÍSTICAS:")
    print(f"- Archivos probados: {len(resultados)}")
    print(f"- Éxitos: {exitos}")
    print(f"- Fallos: {len(resultados) - exitos}")
    print(f"- Tasa de éxito: {exitos/len(resultados)*100:.1f}%")
    
    if exitos == len(resultados):
        print("\n🎉 ¡TODOS LOS ARCHIVOS MFA FUNCIONAN CORRECTAMENTE!")
        print("✅ El comando 'search' está implementado correctamente")
        print("✅ Los archivos están listos para usar con Asterisk")
        return True
    else:
        print(f"\n⚠️  {len(resultados) - exitos} archivo(s) tienen problemas")
        print("❌ Revisar los errores antes de usar en producción")
        return False

def test_syntax():
    """Verificar sintaxis de los archivos MFA"""
    
    print("\n" + "=" * 70)
    print("🔍 VERIFICACIÓN DE SINTAXIS")
    print("=" * 70)
    
    archivos = ["mfa.py", "mfa_simple.py", "mfa_auto.py"]
    
    for archivo in archivos:
        print(f"🔄 Verificando {archivo}...")
        
        try:
            result = subprocess.run(
                ["python3", "-m", "py_compile", archivo],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                print(f"✅ {archivo} - Sintaxis correcta")
            else:
                print(f"❌ {archivo} - Error de sintaxis:")
                print(result.stderr)
                
        except Exception as e:
            print(f"❌ {archivo} - Error: {str(e)}")

if __name__ == "__main__":
    print("BMC REMEDY - PRUEBA DE ARCHIVOS MFA")
    print("Verificando comando 'search' y sintaxis")
    print()
    
    # Verificar sintaxis primero
    test_syntax()
    
    # Probar funcionalidad
    exito = test_mfa_search()
    
    print("\n" + "=" * 70)
    print("🏁 PRUEBA COMPLETADA")
    print("=" * 70)
    
    if exito:
        print("🎉 Archivos MFA listos para Asterisk!")
        sys.exit(0)
    else:
        print("⚠️  Se requieren correcciones")
        sys.exit(1)
