#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de prueba para verificar que los archivos MFA funcionan con Python 3.6
"""

import subprocess
import sys
import time

def test_python36_compatibility():
    """Probar compatibilidad con Python 3.6"""
    
    print("=" * 70)
    print("🐍 PRUEBA DE COMPATIBILIDAD PYTHON 3.6")
    print("=" * 70)
    print()
    
    # Verificar versión de Python disponible
    try:
        result = subprocess.run(['python3.6', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Python 3.6 disponible: {result.stdout.strip()}")
            python_cmd = 'python3.6'
        else:
            print("⚠️  Python 3.6 no disponible, usando python3")
            python_cmd = 'python3'
    except:
        print("⚠️  Python 3.6 no disponible, usando python3")
        python_cmd = 'python3'
    
    print()
    
    # Parámetros de prueba
    cedula = "1152213619"
    descripcion = "Prueba Python 3.6"
    telefono = "3001234567"
    unique_id = "test36"
    limite = "3"
    
    print("📋 PARÁMETROS DE PRUEBA:")
    print(f"- Cédula: {cedula}")
    print(f"- Descripción: {descripcion}")
    print(f"- Límite: {limite} casos")
    print(f"- Python: {python_cmd}")
    print()
    
    # Lista de archivos MFA a probar
    mfa_files = [
        "mfa.py",
        "mfa_simple.py", 
        "mfa_auto.py"
    ]
    
    resultados = []
    
    for archivo in mfa_files:
        print(f"🔄 Probando {archivo} con {python_cmd}...")
        print("-" * 50)
        
        try:
            start_time = time.time()
            
            cmd = [
                python_cmd, archivo,
                "search",
                cedula,
                descripcion,
                telefono,
                unique_id,
                "",  # Email automático
                limite
            ]
            
            print(f"Comando: {' '.join(cmd)}")
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=90
            )
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            print(f"⏱️  Tiempo: {execution_time:.1f}s")
            print(f"🔄 Código: {result.returncode}")
            
            # Verificar si hay errores de sintaxis o importación
            syntax_error = any(error in result.stderr.lower() for error in [
                'syntaxerror', 'invalid syntax', 'unexpected token',
                'importerror', 'modulenotfounderror'
            ])
            
            if result.stdout:
                print("📤 STDOUT (primeras 3 líneas):")
                lines = result.stdout.split('\n')[:3]
                for line in lines:
                    if line.strip():
                        print(f"   {line}")
            
            if result.stderr:
                print("⚠️  STDERR (primeras 3 líneas):")
                lines = result.stderr.split('\n')[:3]
                for line in lines:
                    if line.strip():
                        print(f"   {line}")
            
            # Evaluar resultado
            if syntax_error:
                print("❌ ERROR DE SINTAXIS/IMPORTACIÓN")
                exito = False
            elif result.returncode == 0:
                print("✅ ÉXITO - Compatible con Python 3.6")
                exito = True
            else:
                print("⚠️  FALLO - Pero sin errores de sintaxis")
                exito = False
            
            resultados.append({
                'archivo': archivo,
                'exito': exito,
                'tiempo': execution_time,
                'codigo': result.returncode,
                'syntax_error': syntax_error
            })
            
        except subprocess.TimeoutExpired:
            print("❌ TIMEOUT")
            resultados.append({
                'archivo': archivo,
                'exito': False,
                'tiempo': 90.0,
                'codigo': -1,
                'syntax_error': False
            })
        except Exception as e:
            print(f"❌ ERROR: {str(e)}")
            resultados.append({
                'archivo': archivo,
                'exito': False,
                'tiempo': 0.0,
                'codigo': -2,
                'syntax_error': False
            })
        
        print()
    
    # Resumen final
    print("=" * 70)
    print("📊 RESUMEN DE COMPATIBILIDAD PYTHON 3.6")
    print("=" * 70)
    
    compatibles = 0
    syntax_errors = 0
    
    for resultado in resultados:
        archivo = resultado['archivo']
        exito = resultado['exito']
        tiempo = resultado['tiempo']
        codigo = resultado['codigo']
        syntax_error = resultado['syntax_error']
        
        if syntax_error:
            status = "❌ INCOMPATIBLE"
            syntax_errors += 1
        elif exito:
            status = "✅ COMPATIBLE"
            compatibles += 1
        else:
            status = "⚠️  PROBLEMAS"
        
        print(f"{archivo:15} | {status:15} | {tiempo:6.1f}s | Código: {codigo}")
    
    print()
    print(f"📈 ESTADÍSTICAS:")
    print(f"- Archivos probados: {len(resultados)}")
    print(f"- Compatibles: {compatibles}")
    print(f"- Con errores de sintaxis: {syntax_errors}")
    print(f"- Con otros problemas: {len(resultados) - compatibles - syntax_errors}")
    
    if syntax_errors == 0:
        print("\n🎉 ¡TODOS LOS ARCHIVOS SON COMPATIBLES CON PYTHON 3.6!")
        print("✅ No hay errores de sintaxis o importación")
        print("✅ Los archivos MFA están listos para usar")
        return True
    else:
        print(f"\n⚠️  {syntax_errors} archivo(s) tienen problemas de compatibilidad")
        print("❌ Se requieren correcciones antes de usar en Python 3.6")
        return False

def test_docker_python36():
    """Probar con Docker Python 3.6"""
    
    print("\n" + "=" * 70)
    print("🐳 PRUEBA CON DOCKER PYTHON 3.6")
    print("=" * 70)
    
    print("🔄 Probando mfa.py con Docker Python 3.6...")
    
    try:
        cmd = [
            "sudo", "docker", "run", "--rm",
            "-v", f"{subprocess.check_output(['pwd'], text=True).strip()}:/app",
            "-w", "/app",
            "python:3.6-slim",
            "python", "mfa.py",
            "search",
            "1152213619",
            "Prueba Docker 3.6",
            "3001234567", 
            "docker36",
            "",  # Email automático
            "3"
        ]
        
        print(f"Comando Docker: {' '.join(cmd[:8])} ... [argumentos]")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        print(f"🔄 Código de retorno: {result.returncode}")
        
        if result.stderr:
            syntax_error = any(error in result.stderr.lower() for error in [
                'syntaxerror', 'invalid syntax', 'unexpected token'
            ])
            
            if syntax_error:
                print("❌ ERROR DE SINTAXIS EN DOCKER PYTHON 3.6")
                print("STDERR:")
                print(result.stderr[:500])
                return False
        
        print("✅ COMPATIBLE CON DOCKER PYTHON 3.6")
        return True
        
    except subprocess.TimeoutExpired:
        print("❌ TIMEOUT en Docker")
        return False
    except Exception as e:
        print(f"❌ ERROR en Docker: {str(e)}")
        return False

if __name__ == "__main__":
    print("BMC REMEDY - PRUEBA DE COMPATIBILIDAD PYTHON 3.6")
    print("Verificando que los archivos MFA funcionen con Python 3.6")
    print()
    
    # Prueba de compatibilidad local
    exito_local = test_python36_compatibility()
    
    # Prueba con Docker si la local fue exitosa
    exito_docker = True
    if exito_local:
        exito_docker = test_docker_python36()
    
    print("\n" + "=" * 70)
    print("🏁 PRUEBA DE PYTHON 3.6 COMPLETADA")
    print("=" * 70)
    
    if exito_local and exito_docker:
        print("🎉 Archivos MFA 100% compatibles con Python 3.6!")
        print("✅ Listos para usar en sistemas con Python 3.6")
        sys.exit(0)
    else:
        print("⚠️  Se requieren ajustes para Python 3.6")
        sys.exit(1)
