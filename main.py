#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script principal para automatizar el flujo completo de:
1. Buscar casos en BMC Remedy
2. Generar reporte y enviarlo por correo

Uso:
    python main.py <cedula> [--destinatario <email>] [--todos] [--silent] [--debug]
"""

import sys
import os
import subprocess
import argparse
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Optional, Tuple

# Check for debug mode early
DEBUG_MODE = '--debug' in sys.argv

# Configure logging based on debug mode
if not DEBUG_MODE:
    # Completely disable logging BEFORE importing anything else
    logging.disable(logging.CRITICAL)
    # Also set the root logger to the highest level
    logging.getLogger().setLevel(logging.CRITICAL + 1)

# Import custom modules
from src.config.config import get_config, setup_from_args, is_silent_mode, is_debug_mode
from src.config.logging_config import setup_logging, get_logger, log_operation_start, log_operation_success, log_operation_error




def ejecutar_flujo_completo(cedula: str, destinatario: Optional[str] = None, todos: bool = False, limite: Optional[int] = None) -> bool:
    """
    Ejecuta el flujo completo de búsqueda de casos y envío de reporte

    Args:
        cedula: Número de cédula a consultar
        destinatario: Correo del destinatario (opcional)
        todos: Si es True, muestra todos los incidentes
        limite: Número máximo de incidentes a mostrar (opcional)

    Returns:
        True si el proceso se completó correctamente, False en caso contrario
    """
    logger = get_logger('main.flujo_completo')

    try:
        log_operation_start(logger, "flujo completo", cedula=cedula, destinatario=destinatario, todos=todos)

        # Paso 1: Ejecutar búsqueda de casos en BMC Remedy usando datos en memoria
        success, reporte_data, tickets_count, incidentes_count = _ejecutar_busqueda_casos(cedula, todos, limite, logger)
        if not success:
            return False

        # Paso 1.5: Si no se especificó destinatario, intentar obtenerlo del usuario
        if not destinatario:
            destinatario = _obtener_email_usuario(reporte_data, cedula, logger)
            if destinatario:
                logger.info(f"Usando email del usuario: {destinatario}")

        # Paso 2: Enviar el reporte por correo usando datos en memoria
        success = _ejecutar_envio_reporte(cedula, reporte_data, destinatario, todos, limite, logger)
        if not success:
            return False

        log_operation_success(
            logger, "flujo completo",
            cedula=cedula,
            tickets_encontrados=tickets_count,
            incidentes_encontrados=incidentes_count
        )
        return True

    except Exception as e:
        log_operation_error(logger, "flujo completo", e, cedula=cedula)
        return False


def _ejecutar_busqueda_casos(cedula: str, todos: bool, limite: Optional[int], logger) -> Tuple[bool, dict, int, int]:
    """
    Ejecuta la búsqueda de casos en BMC Remedy usando datos en memoria.

    Args:
        cedula: Número de cédula a consultar
        todos: Si es True, muestra todos los incidentes
        limite: Número máximo de incidentes a mostrar (opcional)
        logger: Logger instance

    Returns:
        Tuple of (success, reporte_data, tickets_count, incidents_count)
    """
    log_operation_start(logger, "búsqueda de casos", cedula=cedula, mostrar_todos=todos)

    try:
        # Importar la función de búsqueda directamente
        from src.core.open import buscar_tickets_por_cedula, mostrar_reporte_unificado

        # Ejecutar búsqueda y obtener datos en memoria
        logger.info("Ejecutando búsqueda de tickets en memoria...")
        reporte_data = buscar_tickets_por_cedula(cedula, todos, limite)

        if not reporte_data:
            logger.error("No se pudieron obtener datos del reporte")
            return False, {}, 0, 0

        # Mostrar el reporte en consola
        mostrar_reporte_unificado(reporte_data, todos, limite)

        # Extraer contadores de los datos
        tickets_count = 0
        incidents_count = 0

        if "tickets" in reporte_data and isinstance(reporte_data["tickets"], dict):
            tickets_count = reporte_data["tickets"].get("total_tickets", 0)

        if "incidents" in reporte_data and isinstance(reporte_data["incidents"], dict):
            incidents_count = reporte_data["incidents"].get("total_incidentes", 0)

        log_operation_success(
            logger, "búsqueda de casos",
            tickets_encontrados=tickets_count,
            incidentes_encontrados=incidents_count
        )

        return True, reporte_data, tickets_count, incidents_count

    except Exception as e:
        logger.error(f"Error en la búsqueda de casos: {str(e)}")
        return False, {}, 0, 0


def _ejecutar_envio_reporte(cedula: str, reporte_data: dict, destinatario: Optional[str], todos: bool, limite: Optional[int], logger) -> bool:
    """
    Ejecuta el envío del reporte por correo usando datos en memoria.

    Args:
        cedula: Número de cédula
        reporte_data: Datos del reporte en memoria
        destinatario: Correo del destinatario
        todos: Si es True, incluye todos los incidentes
        limite: Número máximo de incidentes a incluir
        logger: Logger instance

    Returns:
        True si el envío fue exitoso, False en caso contrario
    """
    log_operation_start(logger, "envío de reporte", cedula=cedula, destinatario=destinatario)

    try:
        # Importar la función de procesamiento directamente
        from src.core.procesar_todo import procesar_y_enviar_desde_datos

        # Ejecutar procesamiento y envío usando datos en memoria
        logger.info(f"Enviando reporte usando datos en memoria...")
        resultado = procesar_y_enviar_desde_datos(cedula, reporte_data, destinatario, todos, limite)

        if resultado:
            log_operation_success(logger, "envío de reporte", destinatario=destinatario)
            return True
        else:
            logger.error("Error en el procesamiento y envío del reporte")
            return False

    except Exception as e:
        logger.error(f"Error en el envío del reporte: {str(e)}")
        return False


def _obtener_email_usuario(reporte_data: dict, cedula: str, logger) -> Optional[str]:
    """
    Obtiene el email del usuario desde los datos del reporte en memoria.

    Args:
        reporte_data: Datos del reporte en memoria
        cedula: Número de cédula del usuario
        logger: Logger instance

    Returns:
        Email del usuario o None si no se encuentra
    """
    try:
        # Obtener información del usuario desde los datos en memoria
        user_info = reporte_data.get("user_info", {})
        if user_info.get("found", False):
            email = user_info.get("email", "").strip()
            if email:
                logger.info(f"Email encontrado para cédula {cedula}: {email}")
                return email
            else:
                logger.warning(f"Usuario encontrado pero sin email para cédula {cedula}")
        else:
            logger.warning(f"Usuario no encontrado en el reporte para cédula {cedula}")

    except Exception as e:
        logger.warning(f"Error al obtener email del usuario: {str(e)}")

    return None


# Función eliminada - ya no necesitamos contar desde archivos


def main():
    """Función principal que procesa los argumentos y ejecuta el flujo"""
    parser = argparse.ArgumentParser(
        description='Buscar casos en BMC Remedy y enviar reporte por correo',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Ejemplos de uso:
  %(prog)s 1152213619
  %(prog)s 1152213619 --destinatario <EMAIL>
  %(prog)s 1152213619 --todos --silent
  %(prog)s 1152213619 --limite 3
  %(prog)s 1152213619 --debug
        """
    )

    # Argumentos principales
    parser.add_argument('cedula', help='Número de cédula a consultar')
    parser.add_argument('--destinatario', '-d', help='Correo del destinatario (opcional)')
    parser.add_argument('--todos', '-t', action='store_true', help='Mostrar todos los incidentes')
    parser.add_argument('--limite', '-l', type=int, help='Número máximo de incidentes a mostrar (ej: --limite 3)')

    # Argumentos de configuración
    parser.add_argument('--silent', '-s', action='store_true', help='Modo silencioso (solo logs de error)')
    parser.add_argument('--debug', action='store_true', help='Modo debug (logs detallados)')
    parser.add_argument('--verbose', '-v', action='store_true', help='Modo verbose (logs detallados)')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], help='Nivel de logging')
    parser.add_argument('--log-output', choices=['console', 'file', 'both', 'silent'], help='Destino de logs')

    args = parser.parse_args()

    try:
        # Setup configuration from arguments
        config = setup_from_args(args)

        # Setup logging with the configuration only if in debug mode
        if DEBUG_MODE:
            setup_logging(
                app_name=config.app_name,
                log_level=config.logging.level,
                output_mode=config.logging.output_mode,
                log_dir=config.logging.log_dir,
                enable_colors=config.logging.enable_colors
            )

        # Get logger after setup only if in debug mode
        if DEBUG_MODE:
            logger = get_logger('main')
        else:
            logger = None

        # Log startup information only if in debug mode
        if DEBUG_MODE and logger:
            log_operation_start(
                logger, "aplicación principal",
                cedula=args.cedula,
                destinatario=args.destinatario,
                todos=args.todos,
                version=config.version,
                debug_mode=config.debug,
                silent_mode=config.silent_mode
            )

        # Ejecutar el flujo completo
        resultado = ejecutar_flujo_completo(args.cedula, args.destinatario, args.todos, args.limite)

        # Salir con código de error si hubo problemas
        if not resultado:
            if DEBUG_MODE and logger:
                logger.error("El proceso terminó con errores")
            sys.exit(1)
        else:
            if DEBUG_MODE and logger:
                log_operation_success(
                    logger, "aplicación principal",
                    cedula=args.cedula,
                    destinatario=args.destinatario
                )

    except KeyboardInterrupt:
        if DEBUG_MODE:
            logger = get_logger('main')
            logger.warning("Proceso interrumpido por el usuario")
        sys.exit(130)  # Standard exit code for Ctrl+C

    except Exception as e:
        if DEBUG_MODE:
            logger = get_logger('main')
            log_operation_error(logger, "aplicación principal", e)
        sys.exit(1)


if __name__ == "__main__":
    main()
