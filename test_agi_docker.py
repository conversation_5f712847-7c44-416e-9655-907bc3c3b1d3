#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para probar la funcionalidad AGI usando Docker
"""

import subprocess
import sys

def test_agi_docker():
    """Prueba la funcionalidad AGI usando el contenedor Docker"""
    
    print("=== PRUEBA AGI CON DOCKER PYTHON 3.6 ===")
    print("Simulando llamada con cédula: 1152213619")
    print("Ejecutando consulta BMC Remedy...")
    print()
    
    try:
        # Comando para ejecutar en el contenedor Docker
        cmd = [
            "sudo", "docker", "run", "--rm",
            "-v", f"{subprocess.check_output(['pwd'], text=True).strip()}/logs:/app/logs",
            "bmc-remedy-py36",
            "python", "main.py", 
            "1152213619",
            "--destinatario", "<EMAIL>",
            "--limite", "3"
        ]
        
        print("Comando ejecutado:")
        print(" ".join(cmd))
        print()
        
        # Ejecutar el comando
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=180)
        
        print("=== RESULTADO ===")
        print(f"Código de retorno: {result.returncode}")
        print()
        
        if result.stdout:
            print("=== SALIDA ESTÁNDAR ===")
            print(result.stdout)
            print()
        
        if result.stderr:
            print("=== ERRORES ===")
            print(result.stderr)
            print()
        
        if result.returncode == 0:
            print("✅ ÉXITO: La consulta BMC Remedy se ejecutó correctamente")
            print("✅ El reporte fue enviado por correo electrónico")
        else:
            print("❌ ERROR: La consulta falló")
            
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("❌ TIMEOUT: La consulta tardó demasiado tiempo")
        return False
    except Exception as e:
        print(f"❌ EXCEPCIÓN: {str(e)}")
        return False

def test_agi_simple():
    """Prueba simple sin Docker"""
    
    print("\n=== PRUEBA AGI SIMPLE (SIN DOCKER) ===")
    print("Simulando llamada con cédula: 1152213619")
    print("Ejecutando consulta BMC Remedy...")
    print()
    
    try:
        cmd = [
            "python3", "main.py", 
            "1152213619",
            "--destinatario", "<EMAIL>",
            "--limite", "3"
        ]
        
        print("Comando ejecutado:")
        print(" ".join(cmd))
        print()
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=180)
        
        print("=== RESULTADO ===")
        print(f"Código de retorno: {result.returncode}")
        print()
        
        if result.stdout:
            print("=== SALIDA ESTÁNDAR ===")
            print(result.stdout)
            print()
        
        if result.stderr:
            print("=== ERRORES ===")
            print(result.stderr)
            print()
        
        if result.returncode == 0:
            print("✅ ÉXITO: La consulta BMC Remedy se ejecutó correctamente")
            print("✅ El reporte fue enviado por correo electrónico")
        else:
            print("❌ ERROR: La consulta falló")
            
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("❌ TIMEOUT: La consulta tardó demasiado tiempo")
        return False
    except Exception as e:
        print(f"❌ EXCEPCIÓN: {str(e)}")
        return False

if __name__ == "__main__":
    print("PRUEBA DE FUNCIONALIDAD AGI PARA BMC REMEDY")
    print("=" * 50)
    
    # Probar con Docker
    docker_success = test_agi_docker()
    
    # Probar sin Docker
    simple_success = test_agi_simple()
    
    print("\n" + "=" * 50)
    print("RESUMEN DE PRUEBAS:")
    print(f"Docker Python 3.6: {'✅ ÉXITO' if docker_success else '❌ FALLO'}")
    print(f"Python local:       {'✅ ÉXITO' if simple_success else '❌ FALLO'}")
    
    if docker_success or simple_success:
        print("\n🎉 Al menos una configuración funciona correctamente!")
        print("La aplicación está lista para integrarse con Asterisk AGI.")
    else:
        print("\n⚠️  Ninguna configuración funcionó. Revisar logs para más detalles.")
