#!/bin/bash

# Script de instalación para integración con Asterisk
# Este script configura el sistema BMC Remedy para trabajar con Asterisk AGI

set -e

echo "=== Instalación de BMC Remedy para Asterisk ==="

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Función para imprimir mensajes
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Verificar que estamos en el directorio correcto
if [ ! -f "mfa.py" ]; then
    print_error "No se encuentra mfa.py. Ejecute este script desde el directorio del proyecto."
    exit 1
fi

# Obtener directorio actual
PROJECT_DIR=$(pwd)
print_status "Directorio del proyecto: $PROJECT_DIR"

# Verificar Python
if ! command -v python3 &> /dev/null; then
    print_error "Python3 no está instalado"
    exit 1
fi

PYTHON_VERSION=$(python3 --version)
print_status "Python encontrado: $PYTHON_VERSION"

# Verificar dependencias
print_status "Verificando dependencias..."
if [ -f "requirements.txt" ]; then
    pip3 install -r requirements.txt
    print_status "Dependencias instaladas"
else
    print_warning "No se encontró requirements.txt"
fi

# Hacer ejecutables los scripts AGI
chmod +x mfa.py
chmod +x mfa_simple.py
print_status "Scripts AGI marcados como ejecutables"

# Crear directorio de logs si no existe
mkdir -p logs
print_status "Directorio de logs creado"

# Detectar directorio de AGI de Asterisk
AGI_DIRS=(
    "/var/lib/asterisk/agi-bin"
    "/usr/share/asterisk/agi-bin"
    "/opt/asterisk/agi-bin"
    "/usr/local/share/asterisk/agi-bin"
)

AGI_DIR=""
for dir in "${AGI_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        AGI_DIR="$dir"
        break
    fi
done

if [ -z "$AGI_DIR" ]; then
    print_warning "No se pudo detectar automáticamente el directorio AGI de Asterisk"
    print_warning "Directorios comunes probados:"
    for dir in "${AGI_DIRS[@]}"; do
        echo "  - $dir"
    done
    echo ""
    print_warning "Por favor, copie manualmente mfa.py al directorio AGI de su instalación de Asterisk"
    print_warning "O cree un enlace simbólico:"
    echo "  sudo ln -s $PROJECT_DIR/mfa.py /ruta/a/asterisk/agi-bin/"
else
    print_status "Directorio AGI encontrado: $AGI_DIR"
    
    # Crear enlace simbólico
    if [ -L "$AGI_DIR/mfa.py" ]; then
        print_warning "El enlace simbólico ya existe, eliminando el anterior..."
        sudo rm "$AGI_DIR/mfa.py"
    fi
    
    print_status "Creando enlaces simbólicos..."
    sudo ln -s "$PROJECT_DIR/mfa.py" "$AGI_DIR/mfa.py"
    sudo ln -s "$PROJECT_DIR/mfa_simple.py" "$AGI_DIR/mfa_simple.py"
    print_status "Enlaces simbólicos creados:"
    print_status "  $AGI_DIR/mfa.py -> $PROJECT_DIR/mfa.py"
    print_status "  $AGI_DIR/mfa_simple.py -> $PROJECT_DIR/mfa_simple.py"
fi

# Verificar permisos
print_status "Verificando permisos..."
if [ -f "$AGI_DIR/mfa.py" ]; then
    sudo chown asterisk:asterisk "$AGI_DIR/mfa.py" 2>/dev/null || print_warning "No se pudieron cambiar los permisos para mfa.py (puede ser normal)"
fi
if [ -f "$AGI_DIR/mfa_simple.py" ]; then
    sudo chown asterisk:asterisk "$AGI_DIR/mfa_simple.py" 2>/dev/null || print_warning "No se pudieron cambiar los permisos para mfa_simple.py (puede ser normal)"
fi

# Crear archivo de configuración de ejemplo para Asterisk
cat > asterisk_dialplan_example.conf << 'EOF'
; Ejemplo de configuración para extensions.conf
; Agregar esto a su dialplan de Asterisk

[bmc-remedy-context]
; Ejemplo de uso del script AGI
exten => _X.,1,NoOp(Iniciando búsqueda BMC Remedy)
same => n,Set(DOCUMENT=${CALLERID(num)})  ; O la variable que contenga la cédula
same => n,Set(TICKET_DETAIL="Consulta telefónica")
same => n,Set(CELLPHONE=${CALLERID(num)})
same => n,Set(UNIQUE_ID=${STRREPLACE(UNIQUEID,".","")})
same => n,AGI(mfa.py,"create","${DOCUMENT}","${TICKET_DETAIL}","${CELLPHONE}","${UNIQUE_ID}","<EMAIL>","5")
same => n,NoOp(Resultado: ${TICKET_SEARCH_RESULT})
same => n,NoOp(Mensaje: ${TICKET_SEARCH_MESSAGE})
same => n,GotoIf($["${TICKET_SEARCH_RESULT}" = "SUCCESS"]?success:failed)
same => n(success),Playback(custom/reporte-enviado)
same => n,Hangup()
same => n(failed),Playback(custom/error-reporte)
same => n,Hangup()

; Ejemplo más avanzado con validación
[bmc-remedy-advanced]
exten => _X.,1,NoOp(=== BMC Remedy Lookup ===)
same => n,Set(DOCUMENT=${ARG1})  ; Recibir cédula como argumento
same => n,GotoIf($[${LEN(${DOCUMENT})} < 8]?invalid:valid)
same => n(invalid),Playback(custom/cedula-invalida)
same => n,Hangup()
same => n(valid),Set(TICKET_DETAIL="Consulta IVR - ${STRFTIME(${EPOCH},,%Y-%m-%d %H:%M:%S)}")
same => n,Set(CELLPHONE=${CALLERID(num)})
same => n,Set(UNIQUE_ID=${STRREPLACE(UNIQUEID,".","")})
same => n,Playback(custom/buscando-tickets)
same => n,AGI(mfa.py,"create","${DOCUMENT}","${TICKET_DETAIL}","${CELLPHONE}","${UNIQUE_ID}","<EMAIL>","10")
same => n,NoOp(=== Resultado AGI ===)
same => n,NoOp(TICKET_SEARCH_RESULT: ${TICKET_SEARCH_RESULT})
same => n,NoOp(TICKET_SEARCH_MESSAGE: ${TICKET_SEARCH_MESSAGE})
same => n,GotoIf($["${TICKET_SEARCH_RESULT}" = "SUCCESS"]?success:error)
same => n(success),Playback(custom/reporte-enviado-exitosamente)
same => n,SayDigits(${DOCUMENT})
same => n,Hangup()
same => n(error),Playback(custom/error-al-generar-reporte)
same => n,Verbose(1,Error: ${TICKET_SEARCH_MESSAGE})
same => n,Hangup()
EOF

print_status "Archivo de ejemplo creado: asterisk_dialplan_example.conf"

# Crear script de prueba
cat > test_agi.py << 'EOF'
#!/usr/bin/env python3
"""
Script de prueba para el AGI de BMC Remedy
Simula una llamada desde Asterisk
"""

import subprocess
import sys
import os

def test_agi():
    print("=== Prueba del script AGI ===")
    
    # Simular variables AGI
    agi_env = """agi_request: mfa.py
agi_channel: SIP/test-********
agi_language: es
agi_type: SIP
agi_uniqueid: **********.123
agi_callerid: **********
agi_calleridname: Test User
agi_callingpres: 0
agi_callingani2: 0
agi_callington: 0
agi_callingtns: 0
agi_dnid: unknown
agi_rdnis: unknown
agi_context: default
agi_extension: test
agi_priority: 1
agi_enhanced: 0.0
agi_accountcode: 
agi_threadid: ************

"""
    
    # Ejecutar script AGI
    cmd = ['python3.11', 'mfa.py', 'create', '**********', 'Test ticket detail', '**********', '*************', '<EMAIL>', '3']
    
    print(f"Ejecutando: {' '.join(cmd)}")
    print("Entrada AGI simulada:")
    print(agi_env)
    
    try:
        process = subprocess.Popen(
            cmd,
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        stdout, stderr = process.communicate(input=agi_env)
        
        print("=== STDOUT ===")
        print(stdout)
        
        if stderr:
            print("=== STDERR ===")
            print(stderr)
        
        print(f"=== Código de salida: {process.returncode} ===")
        
        if process.returncode == 0:
            print("✅ Prueba exitosa")
        else:
            print("❌ Prueba falló")
            
    except Exception as e:
        print(f"❌ Error ejecutando prueba: {e}")

if __name__ == "__main__":
    test_agi()
EOF

chmod +x test_agi.py
print_status "Script de prueba creado: test_agi.py"

echo ""
print_status "=== Instalación completada ==="
echo ""
print_status "Próximos pasos:"
echo "1. Revise el archivo 'asterisk_dialplan_example.conf' para ejemplos de configuración"
echo "2. Agregue la configuración apropiada a su extensions.conf"
echo "3. Ejecute 'python3 test_agi.py' para probar la funcionalidad"
echo "4. Reinicie Asterisk: sudo systemctl restart asterisk"
echo ""
print_status "Los scripts AGI están listos para usar desde Asterisk:"
echo ""
print_status "Para Python 3.7+ (recomendado):"
echo "same => n,AGI(mfa.py,\"create\",\"\${document}\",\"\${ticketDetail}\",\"\${cellphone}\",\"\${STRREPLACE(UNIQUEID,\".\",\"\")}\",\"<EMAIL>\",\"5\")"
echo ""
print_status "Para Python 2.7+ / versiones anteriores (compatible):"
echo "same => n,AGI(mfa_simple.py,\"create\",\"\${document}\",\"\${ticketDetail}\",\"\${cellphone}\",\"\${STRREPLACE(UNIQUEID,\".\",\"\")}\",\"<EMAIL>\",\"5\")"
echo ""
print_warning "Asegúrese de que:"
echo "- El usuario 'asterisk' tenga permisos de lectura en este directorio"
echo "- Las variables de entorno estén configuradas correctamente"
echo "- Los archivos de configuración (.env) estén accesibles"
echo "- Use mfa_simple.py si tiene problemas con dataclasses o Python < 3.7"
