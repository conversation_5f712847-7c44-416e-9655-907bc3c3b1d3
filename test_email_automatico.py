#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de prueba para verificar que el email automático del usuario funciona correctamente
"""

import subprocess
import sys
import time
import re

def test_email_automatico():
    """Probar que el sistema usa el email del usuario de BMC Remedy automáticamente"""
    
    print("=" * 70)
    print("📧 PRUEBA DE EMAIL AUTOMÁTICO DESDE BMC REMEDY")
    print("=" * 70)
    print()
    
    # Parámetros de prueba
    cedula = "1152213619"  # <PERSON>
    
    print("📋 CONFIGURACIÓN DE PRUEBA:")
    print(f"- Cédula: {cedula} (<PERSON>)")
    print("- Email: NO especificado (debe obtenerlo automáticamente de BMC)")
    print("- Límite: 3 registros")
    print()
    
    try:
        print("🔍 Ejecutando consulta SIN especificar email...")
        start_time = time.time()
        
        # Ejecutar SIN especificar destinatario
        cmd = [
            "sudo", "docker", "run", "--rm",
            "-v", f"{subprocess.check_output(['pwd'], text=True).strip()}/logs:/app/logs",
            "bmc-remedy-py36-fixed-final",
            "python", "main.py", 
            cedula,
            "--limite", "3",
            "--debug"  # Para ver los logs de email
        ]
        
        print("Comando ejecutado:")
        print(" ".join(cmd))
        print()
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print("=" * 70)
        print("📊 ANÁLISIS DE RESULTADOS")
        print("=" * 70)
        
        print(f"⏱️  Tiempo de ejecución: {execution_time:.1f} segundos")
        print(f"🔄 Código de retorno: {result.returncode}")
        print()
        
        # Analizar la salida para encontrar información de email
        output_text = result.stdout + result.stderr
        
        # Buscar logs relacionados con email
        email_logs = []
        for line in output_text.split('\n'):
            if any(keyword in line.lower() for keyword in ['email', 'correo', 'destinatario', 'usuario']):
                email_logs.append(line.strip())
        
        print("📧 LOGS RELACIONADOS CON EMAIL:")
        print("-" * 50)
        if email_logs:
            for log in email_logs[:10]:  # Mostrar solo los primeros 10
                print(f"  {log}")
        else:
            print("  No se encontraron logs específicos de email")
        print()
        
        # Buscar patrones específicos
        email_encontrado = None
        email_usado = None
        
        # Buscar "Email encontrado para cédula"
        email_pattern = r"Email encontrado para cédula \d+: (.+)"
        match = re.search(email_pattern, output_text)
        if match:
            email_encontrado = match.group(1).strip()
        
        # Buscar "Usando email del usuario"
        using_pattern = r"Usando email del usuario: (.+)"
        match = re.search(using_pattern, output_text)
        if match:
            email_usado = match.group(1).strip()
        
        # Buscar "enviado correctamente"
        correo_enviado = "enviado correctamente" in output_text.lower()
        
        print("=" * 70)
        print("✅ VERIFICACIÓN DE EMAIL AUTOMÁTICO")
        print("=" * 70)
        
        print(f"🔍 Email encontrado en BMC: {email_encontrado if email_encontrado else '❌ NO DETECTADO'}")
        print(f"📧 Email usado para envío: {email_usado if email_usado else '❌ NO DETECTADO'}")
        print(f"📤 Correo enviado exitosamente: {'✅ SÍ' if correo_enviado else '❌ NO'}")
        print(f"⚡ Tiempo aceptable (<30s): {'✅ SÍ' if execution_time < 30 else '❌ NO'}")
        print(f"🔧 Ejecución exitosa: {'✅ SÍ' if result.returncode == 0 else '❌ NO'}")
        
        print()
        print("=" * 70)
        print("📋 RESUMEN DE EMAIL AUTOMÁTICO")
        print("=" * 70)
        
        if email_encontrado and email_usado and correo_enviado:
            print("🎉 ¡EMAIL AUTOMÁTICO FUNCIONANDO PERFECTAMENTE!")
            print(f"✅ El sistema encontró el email del usuario en BMC: {email_encontrado}")
            print(f"✅ Usó automáticamente ese email para el envío: {email_usado}")
            print("✅ El correo se envió exitosamente")
            print()
            print("🚀 LISTO PARA ASTERISK SIN ESPECIFICAR EMAIL")
            return True
        else:
            print("⚠️  PROBLEMAS DETECTADOS EN EMAIL AUTOMÁTICO:")
            if not email_encontrado:
                print("❌ No se detectó que se encontrara email en BMC Remedy")
            if not email_usado:
                print("❌ No se detectó que se usara email del usuario")
            if not correo_enviado:
                print("❌ El correo no se envió correctamente")
            
            # Mostrar más detalles para debugging
            print("\n📝 SALIDA COMPLETA PARA DEBUGGING:")
            print("-" * 50)
            print(output_text[-1000:])  # Últimas 1000 caracteres
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ TIMEOUT: La consulta tardó más de 2 minutos")
        return False
    except Exception as e:
        print(f"❌ EXCEPCIÓN: {str(e)}")
        return False

def test_comparacion_emails():
    """Comparar comportamiento con y sin email especificado"""
    
    print("\n" + "=" * 70)
    print("🔄 COMPARACIÓN: CON EMAIL vs SIN EMAIL")
    print("=" * 70)
    
    cedula = "1152213619"
    email_manual = "<EMAIL>"
    
    print(f"Probando cédula {cedula} con dos enfoques:")
    print(f"1. CON email especificado: {email_manual}")
    print("2. SIN email especificado (automático desde BMC)")
    print()
    
    # Prueba 1: Con email especificado
    print("🔄 Prueba 1: CON email especificado...")
    try:
        cmd1 = [
            "sudo", "docker", "run", "--rm",
            "-v", f"{subprocess.check_output(['pwd'], text=True).strip()}/logs:/app/logs",
            "bmc-remedy-py36-fixed-final",
            "python", "main.py", 
            cedula,
            "--destinatario", email_manual,
            "--limite", "3"
        ]
        
        result1 = subprocess.run(cmd1, capture_output=True, text=True, timeout=60)
        exito1 = result1.returncode == 0 and "enviado correctamente" in (result1.stdout + result1.stderr)
        print(f"   Resultado: {'✅ ÉXITO' if exito1 else '❌ FALLO'}")
        
    except Exception as e:
        print(f"   Resultado: ❌ ERROR - {str(e)}")
        exito1 = False
    
    # Prueba 2: Sin email especificado
    print("🔄 Prueba 2: SIN email especificado...")
    try:
        cmd2 = [
            "sudo", "docker", "run", "--rm",
            "-v", f"{subprocess.check_output(['pwd'], text=True).strip()}/logs:/app/logs",
            "bmc-remedy-py36-fixed-final",
            "python", "main.py", 
            cedula,
            "--limite", "3"
        ]
        
        result2 = subprocess.run(cmd2, capture_output=True, text=True, timeout=60)
        exito2 = result2.returncode == 0 and "enviado correctamente" in (result2.stdout + result2.stderr)
        print(f"   Resultado: {'✅ ÉXITO' if exito2 else '❌ FALLO'}")
        
    except Exception as e:
        print(f"   Resultado: ❌ ERROR - {str(e)}")
        exito2 = False
    
    print(f"\n📊 COMPARACIÓN FINAL:")
    print(f"- Con email manual: {'✅ Funciona' if exito1 else '❌ Falla'}")
    print(f"- Con email automático: {'✅ Funciona' if exito2 else '❌ Falla'}")
    
    if exito1 and exito2:
        print("\n🎉 ¡AMBOS MÉTODOS FUNCIONAN CORRECTAMENTE!")
        print("✅ Puedes usar el sistema con o sin especificar email")
        return True
    else:
        print("\n⚠️  Hay problemas con uno o ambos métodos")
        return False

if __name__ == "__main__":
    print("BMC REMEDY - PRUEBA DE EMAIL AUTOMÁTICO")
    print("Verificando que el sistema use el email del usuario desde BMC")
    print()
    
    # Prueba principal
    exito_principal = test_email_automatico()
    
    # Prueba comparativa
    if exito_principal:
        test_comparacion_emails()
    
    print("\n" + "=" * 70)
    print("🏁 PRUEBA DE EMAIL AUTOMÁTICO COMPLETADA")
    print("=" * 70)
    
    if exito_principal:
        print("🎉 Email automático funcionando correctamente!")
        print("📧 El sistema usa el email del usuario desde BMC Remedy")
        sys.exit(0)
    else:
        print("⚠️  Se requieren ajustes en el email automático")
        sys.exit(1)
