2025-07-17 10:43:33 - MFA_AUTO - Detectado: mfa.py - Python 3.11 con dataclasses disponible
2025-07-17 10:43:33 - MFA_AUTO - Ejecutando: /opt/anaconda3/bin/python3.11 /Users/<USER>/Documents/esta/bmc-remedy-organized/mfa.py create 1152213619 Test auto 3001234567 123456789 <EMAIL> 2
2025-07-17 10:45:07 - MFA_AUTO - Script mfa.py terminó con código: 0
2025-07-17 10:49:31 - MFA_AUTO - Detectado: mfa.py - Python 3.11 con dataclasses disponible
2025-07-17 10:49:31 - MFA_AUTO - Ejecutando: /opt/anaconda3/bin/python3.11 /Users/<USER>/Documents/esta/bmc-remedy-organized/mfa.py create 1152213619 Test auto 3001234567 123456789 <EMAIL> 2
2025-07-17 15:08:54 - MFA_AUTO - Detectado: mfa.py - Python 3.10 con dataclasses disponible
2025-07-17 15:08:54 - MFA_AUTO - Ejecutando: /usr/bin/python3 /home/<USER>/Downloads/bmc2/bmc-remedy-organized/mfa.py search 1152213619 Prueba desde script 3001234567 test123456 <EMAIL> 3
2025-07-17 15:08:54 - MFA_AUTO - Script mfa.py terminó con código: 1
2025-07-17 15:10:54 - MFA_AUTO - Detectado: mfa.py - Python 3.10 con dataclasses disponible
2025-07-17 15:10:54 - MFA_AUTO - Ejecutando: /usr/bin/python3 /home/<USER>/Downloads/bmc2/bmc-remedy-organized/mfa.py search 1152213619 Prueba desde script 3001234567 test123456 <EMAIL> 3
2025-07-17 15:11:33 - MFA_AUTO - Script mfa.py terminó con código: 0
2025-07-17 16:20:43 - MFA_AUTO - Detectado: mfa.py - Python 3.10 con dataclasses disponible
2025-07-17 16:20:43 - MFA_AUTO - Ejecutando: /usr/bin/python3 /home/<USER>/Downloads/bmc2/bmc-remedy-organized/mfa.py search 1152213619 Prueba Python 3.6 3001234567 test36  3
2025-07-17 16:21:03 - MFA_AUTO - Script mfa.py terminó con código: 0
