# BMC Remedy Container

Este proyecto automatiza la búsqueda de tickets en BMC Remedy y el envío de reportes por correo electrónico.

## 🚀 Características

- ✅ Búsqueda automática de tickets por cédula
- ✅ Generación de reportes HTML
- ✅ Envío automático por correo electrónico
- ✅ Configuración mediante variables de entorno
- ✅ Logging detallado con rotación
- ✅ Soporte para múltiples formatos de salida
- ✅ **Fe<PERSON>s convertidas a hora colombiana (UTC-5)**
- ✅ Ejecución en contenedor Docker (opcional)

## ⚙️ Instalación

### Opción 1: Instalación Local (Recomendada)

1. **Instalar dependencias:**
```bash
pip install -r requirements.txt
```

2. **Configurar variables de entorno:**
El archivo `.env` ya está configurado con los valores correctos. Si necesitas modificar algo:

```bash
# BMC Remedy API Configuration
API_BASE_URL=https://surasoporteti-restapi.onbmc.com/api
LOGIN_URL=https://surasoporteti-restapi.onbmc.com/api/jwt/login
USERNAME_=Integracion.VoiceBot
PASSWORD=$ur@2025*

# Email Configuration
MAIL_URL=https://mosaico.arus.com.co:3000/mailer/correo/notificacion
EMAIL_TO=<EMAIL>
```

### Opción 2: Docker (Opcional)

1. **Construir imagen:**
```bash
docker-compose build
```

## 🔧 Uso

### Ejecución Local (Recomendada)

#### Búsqueda básica (usa email del usuario encontrado)
```bash
python main.py <cedula>
```

#### Con destinatario específico
```bash
python main.py <cedula> --destinatario <EMAIL>
```

#### Mostrar todos los incidentes (sin límite)
```bash
python main.py <cedula> --todos
```

#### Limitar número de incidentes mostrados
```bash
python main.py <cedula> --limite 3
```

### Ejemplos Completos

```bash
# Búsqueda completa con todos los tickets para un destinatario específico
python main.py 1152213619 --destinatario <EMAIL> --todos

# Búsqueda limitada con debug activado
python main.py 1152213619 --limite 5 --debug

# Búsqueda silenciosa
python main.py 1152213619 --destinatario <EMAIL> --silent
```

### Ejecución con Docker (Opcional)

```bash
# Consultar casos para una cédula específica
docker-compose run --rm bmc-remedy-app python main.py 1152213619 --destinatario <EMAIL>

# Consultar todos los incidentes
docker-compose run --rm bmc-remedy-app python main.py 1152213619 --destinatario <EMAIL> --todos
```

## Estructura del Proyecto

```
bmc-remedy-organized/
├── Dockerfile              # Definición de la imagen Docker
├── docker-compose.yml      # Configuración de servicios
├── requirements.txt        # Dependencias de Python
├── README.md               # Este archivo
├── main.py                 # Script principal
├── src/                    # Código fuente organizado
│   ├── config/             # Módulos de configuración
│   │   ├── config.py       # Configuración centralizada
│   │   └── logging_config.py # Configuración de logging
│   ├── core/               # Funcionalidad principal
│   │   ├── open.py         # Módulo de búsqueda en BMC
│   │   ├── procesar_todo.py # Procesamiento de reportes
│   │   └── a.py            # Gestor de tickets BMC
│   └── utils/              # Utilidades
│       ├── enviar_reporte.py # Generación y envío de reportes
│       ├── mail.py         # Módulo de envío de correos
│       └── display.py      # Funciones de visualización
├── logs/                   # Directorio de logs (montado como volumen)
└── docs/                   # Documentación adicional
```

## Uso

### Comando básico
```bash
docker-compose run --rm bmc-remedy-app python main.py <cedula> [opciones]
```

### Opciones disponibles
- `--destinatario <email>`: Especifica el destinatario del reporte
- `--todos`: Muestra todos los incidentes (no solo los primeros 5)
- `--limite <número>`: Especifica cuántos incidentes mostrar (ej: --limite 3)

### Ejemplos
```bash
# Consulta básica
docker-compose run --rm bmc-remedy-app python main.py 1152213619

# Consulta con destinatario específico
docker-compose run --rm bmc-remedy-app python main.py 1152213619 --destinatario <EMAIL>

# Consulta mostrando todos los incidentes
docker-compose run --rm bmc-remedy-app python main.py 1152213619 --todos

# Consulta mostrando solo los 3 últimos incidentes
docker-compose run --rm bmc-remedy-app python main.py 1152213619 --limite 3
```

## Logs

Los logs se almacenan en el directorio `./logs` y son persistentes entre ejecuciones del contenedor.

## Notas

- Los títulos de las notas ahora aparecen en español
- **Las fechas de creación y modificación se muestran en hora colombiana (UTC-5)**
- El contenedor incluye todas las dependencias necesarias
- La configuración se maneja a través de variables de entorno
- Los reportes se envían automáticamente por correo electrónico

## Conversión de Fechas

Las fechas en los reportes HTML se convierten automáticamente de UTC a hora colombiana:
- **UTC-5**: Colombia está 5 horas detrás de UTC
- **Formato**: DD/MM/YYYY HH:MM (COL)
- **Ejemplo**: 2025-02-16T01:29:50 UTC → 15/02/2025 20:29 (COL)
