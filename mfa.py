#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script AGI para Asterisk - Integración con BMC Remedy
Maneja la búsqueda de tickets y envío de reportes desde Asterisk

Uso desde Asterisk:
same => n,AGI(mfa.py, "search", "${document}", "${descripcion}", "${cellphone}", "${STRREPLACE(UNIQUEID,".","")}", "${destinatario}", "${limite}")

Parámetros:
- action: "search" (acción a realizar - buscar tickets)
- document: Número de cédula del usuario
- descripcion: Descripción de la consulta (ej: "Consulta IVR", "Llamada entrante", etc.)
- cellphone: Número de teléfono del llamante
- uniqueId: ID único de la llamada de Asterisk
- destinatario: Email del destinatario (opcional, si no se especifica se obtiene del usuario)
- limite: <PERSON><PERSON>mero máximo de casos a incluir (opcional, por defecto 5)

Ejemplos:
same => n,AGI(mfa.py, "search", "1152213619", "Consulta IVR", "3001234567", "123456789", "<EMAIL>", "10")
same => n,AGI(mfa.py, "search", "1152213619", "Llamada entrante", "3001234567", "123456789", "<EMAIL>")
"""

import sys
import os
import logging
import json
from datetime import datetime
from pathlib import Path

# Agregar el directorio del proyecto al path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Importar módulos del proyecto
from main import ejecutar_flujo_completo
from src.config.config import setup_from_args
from src.utils.enviar_reporte import enviar_reporte_desde_datos

class AGIHandler:
    """Manejador para comandos AGI de Asterisk"""
    
    def __init__(self):
        self.agi_env = {}
        self.setup_logging()
        self.read_agi_environment()
    
    def setup_logging(self):
        """Configurar logging para AGI"""
        log_file = project_root / "logs" / "agi.log"
        log_file.parent.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - AGI - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('AGI')
    
    def read_agi_environment(self):
        """Leer variables de entorno de AGI"""
        try:
            # Solo leer si estamos siendo ejecutados desde Asterisk
            # (stdin no es un terminal)
            if not sys.stdin.isatty():
                while True:
                    line = sys.stdin.readline().strip()
                    if not line:
                        break

                    if ':' in line:
                        key, value = line.split(':', 1)
                        self.agi_env[key.strip()] = value.strip()

                self.logger.info(f"AGI Environment: {self.agi_env}")
            else:
                self.logger.info("Ejecutándose en modo terminal (no AGI)")
        except Exception as e:
            self.logger.error(f"Error reading AGI environment: {e}")
    
    def agi_response(self, message):
        """Enviar respuesta a Asterisk"""
        print(message)
        sys.stdout.flush()
        self.logger.info(f"AGI Response: {message}")
    
    def set_variable(self, variable, value):
        """Establecer variable en Asterisk"""
        self.agi_response(f'SET VARIABLE {variable} "{value}"')
    
    def verbose(self, message, level=1):
        """Enviar mensaje verbose a Asterisk"""
        self.agi_response(f'VERBOSE "{message}" {level}')
    
    def search_tickets_report(self, document, descripcion, cellphone, unique_id, destinatario=None, limite=5):
        """
        Buscar tickets y generar reporte para el documento especificado

        Args:
            document: Número de cédula del usuario
            descripcion: Descripción de la consulta (ej: "Consulta IVR", "Llamada entrante")
            cellphone: Número de teléfono del llamante
            unique_id: ID único de la llamada de Asterisk
            destinatario: Email del destinatario (opcional)
            limite: Número máximo de casos a incluir (opcional, por defecto 5)
        """
        try:
            self.logger.info(f"Iniciando búsqueda para documento: {document}")
            self.verbose(f"Buscando tickets para documento: {document}")
            
            # Configurar argumentos simulados para el sistema
            class MockArgs:
                def __init__(self):
                    self.cedula = document
                    self.destinatario = None  # Se obtendrá del usuario
                    self.todos = False
                    self.limite = 5  # Límite por defecto
                    self.debug = False
                    self.silent = True
                    self.verbose = False
                    self.log_level = 'ERROR'
                    self.log_output = 'silent'
            
            # Habilitar logging para AGI (necesario para debugging)
            logging.disable(logging.NOTSET)
            
            # Ejecutar búsqueda de tickets usando subprocess para evitar problemas de importación
            import subprocess
            cmd = [
                'python3', 'main.py',
                document,
                '--limite', str(limite)
            ]

            if destinatario:
                cmd.extend(['--destinatario', destinatario])

            self.logger.info(f"Ejecutando comando: {' '.join(cmd)}")

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=project_root,
                timeout=120
            )

            self.logger.info(f"Código de salida: {result.returncode}")
            if result.stdout:
                self.logger.info(f"STDOUT: {result.stdout}")
            if result.stderr:
                self.logger.error(f"STDERR: {result.stderr}")

            resultado = result.returncode == 0
            
            if resultado:
                self.set_variable("TICKET_SEARCH_RESULT", "SUCCESS")
                self.set_variable("TICKET_SEARCH_MESSAGE", f"Reporte generado exitosamente para {document}")
                self.verbose(f"Reporte generado exitosamente para documento {document}")
                self.logger.info(f"Reporte generado exitosamente para documento: {document}")
                return True
            else:
                self.set_variable("TICKET_SEARCH_RESULT", "FAILED")
                self.set_variable("TICKET_SEARCH_MESSAGE", f"Error al generar reporte para {document}")
                self.verbose(f"Error al generar reporte para documento {document}")
                self.logger.error(f"Error al generar reporte para documento: {document}")
                return False
                
        except Exception as e:
            error_msg = f"Error en create_ticket_report: {str(e)}"
            self.logger.error(error_msg)
            # También escribir a stderr para debugging
            print(f"ERROR: {error_msg}", file=sys.stderr)
            self.set_variable("TICKET_SEARCH_RESULT", "ERROR")
            self.set_variable("TICKET_SEARCH_MESSAGE", error_msg)
            self.verbose(f"Error: {error_msg}")
            return False
    
    def handle_command(self, action, *args):
        """Manejar comando AGI"""
        try:
            self.logger.info(f"Comando recibido: {action} con argumentos: {args}")
            
            if action == "search":
                if len(args) >= 4:
                    document, descripcion, cellphone, unique_id = args[:4]
                    # Verificar argumentos opcionales
                    destinatario = args[4] if len(args) > 4 else None
                    limite = int(args[5]) if len(args) > 5 and args[5].isdigit() else 5

                    self.logger.info(f"Búsqueda de tickets - Cédula: {document}, Destinatario: {destinatario}, Límite: {limite}")
                    self.logger.info(f"Descripción: {descripcion}, Teléfono: {cellphone}, ID único: {unique_id}")

                    return self.search_tickets_report(document, descripcion, cellphone, unique_id, destinatario, limite)
                else:
                    error_msg = f"Argumentos insuficientes para 'search'. Recibidos: {len(args)}, esperados: 4 (destinatario y límite opcionales)"
                    self.logger.error(error_msg)
                    self.set_variable("TICKET_SEARCH_RESULT", "ERROR")
                    self.set_variable("TICKET_SEARCH_MESSAGE", error_msg)
                    return False
            else:
                error_msg = f"Acción no reconocida: {action}"
                self.logger.error(error_msg)
                self.set_variable("TICKET_SEARCH_RESULT", "ERROR")
                self.set_variable("TICKET_SEARCH_MESSAGE", error_msg)
                return False
                
        except Exception as e:
            error_msg = f"Error manejando comando: {str(e)}"
            self.logger.error(error_msg)
            self.set_variable("TICKET_SEARCH_RESULT", "ERROR")
            self.set_variable("TICKET_SEARCH_MESSAGE", error_msg)
            return False

def main():
    """Función principal del script AGI"""
    try:
        # Crear manejador AGI
        agi = AGIHandler()
        
        # Obtener argumentos de la línea de comandos
        if len(sys.argv) < 2:
            agi.logger.error("No se proporcionó acción")
            agi.set_variable("TICKET_SEARCH_RESULT", "ERROR")
            agi.set_variable("TICKET_SEARCH_MESSAGE", "No se proporcionó acción")
            sys.exit(1)
        
        action = sys.argv[1]
        args = sys.argv[2:] if len(sys.argv) > 2 else []
        
        # Ejecutar comando
        success = agi.handle_command(action, *args)
        
        # Salir con código apropiado
        sys.exit(0 if success else 1)
        
    except Exception as e:
        logging.error(f"Error fatal en main: {str(e)}")
        print(f'SET VARIABLE TICKET_SEARCH_RESULT "FATAL_ERROR"')
        print(f'SET VARIABLE TICKET_SEARCH_MESSAGE "Error fatal: {str(e)}"')
        sys.exit(1)

if __name__ == "__main__":
    main()
