2025-07-17 09:40:36,642 - AGI - INFO - Ejecutando comando: python3 main.py 1152213619 --limite 5 --destinatario <EMAIL>
2025-07-17 09:40:36,716 - AGI - INFO - <PERSON><PERSON><PERSON> de salida: 1
2025-07-17 09:40:36,717 - AGI - ERROR - STDERR: /Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/__init__.py:35: NotOpenSSLWarning: urllib3 v2 only supports OpenSSL 1.1.1+, currently the 'ssl' module is compiled with 'LibreSSL 2.8.3'. See: https://github.com/urllib3/urllib3/issues/3020
  warnings.warn(

2025-07-17 09:40:36,717 - AGI - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_RESULT "FAILED"
2025-07-17 09:40:36,717 - AGI - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_MESSAGE "Error al generar reporte para 1152213619"
2025-07-17 09:40:36,717 - AGI - INFO - AGI Response: VERBOSE "Error al generar reporte para documento 1152213619" 1
2025-07-17 09:40:36,717 - AGI - ERROR - Error al generar reporte para documento: 1152213619
2025-07-17 09:41:17,423 - AGI - INFO - Ejecutando comando: python3.11 main.py 1152213619 --limite 5 --destinatario <EMAIL>
2025-07-17 09:42:53,717 - AGI - INFO - Código de salida: 0
2025-07-17 09:42:53,717 - AGI - INFO - STDOUT: Reporte enviado <NAME_EMAIL>

2025-07-17 09:42:53,717 - AGI - ERROR - STDERR: /opt/anaconda3/lib/python3.11/site-packages/urllib3/connectionpool.py:1063: InsecureRequestWarning: Unverified HTTPS request is being made to host 'mosaico.arus.com.co'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/1.26.x/advanced-usage.html#ssl-warnings
  warnings.warn(

2025-07-17 09:42:53,717 - AGI - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_RESULT "SUCCESS"
2025-07-17 09:42:53,717 - AGI - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_MESSAGE "Reporte generado exitosamente para 1152213619"
2025-07-17 09:42:53,718 - AGI - INFO - AGI Response: VERBOSE "Reporte generado exitosamente para documento 1152213619" 1
2025-07-17 09:42:53,718 - AGI - INFO - Reporte generado exitosamente para documento: 1152213619
2025-07-17 09:49:37,387 - AGI - INFO - Ejecutando comando: python3.11 main.py 1152213619 --limite 3 --destinatario <EMAIL>
2025-07-17 09:51:12,850 - AGI - INFO - Código de salida: 0
2025-07-17 09:51:12,851 - AGI - INFO - STDOUT: Reporte enviado <NAME_EMAIL>

2025-07-17 09:51:12,851 - AGI - ERROR - STDERR: /opt/anaconda3/lib/python3.11/site-packages/urllib3/connectionpool.py:1063: InsecureRequestWarning: Unverified HTTPS request is being made to host 'mosaico.arus.com.co'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/1.26.x/advanced-usage.html#ssl-warnings
  warnings.warn(

2025-07-17 09:51:12,851 - AGI - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_RESULT "SUCCESS"
2025-07-17 09:51:12,851 - AGI - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_MESSAGE "Reporte generado exitosamente para 1152213619"
2025-07-17 09:51:12,851 - AGI - INFO - AGI Response: VERBOSE "Reporte generado exitosamente para documento 1152213619" 1
2025-07-17 09:51:12,851 - AGI - INFO - Reporte generado exitosamente para documento: 1152213619
2025-07-17 09:51:20,806 - AGI - INFO - Ejecutando comando: python3.11 main.py 1152213619 --limite 1 --destinatario <EMAIL>
2025-07-17 09:52:57,058 - AGI - INFO - Código de salida: 0
2025-07-17 09:52:57,059 - AGI - INFO - STDOUT: Reporte enviado <NAME_EMAIL>

2025-07-17 09:52:57,059 - AGI - ERROR - STDERR: /opt/anaconda3/lib/python3.11/site-packages/urllib3/connectionpool.py:1063: InsecureRequestWarning: Unverified HTTPS request is being made to host 'mosaico.arus.com.co'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/1.26.x/advanced-usage.html#ssl-warnings
  warnings.warn(

2025-07-17 09:52:57,059 - AGI - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_RESULT "SUCCESS"
2025-07-17 09:52:57,059 - AGI - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_MESSAGE "Reporte generado exitosamente para 1152213619"
2025-07-17 09:52:57,059 - AGI - INFO - AGI Response: VERBOSE "Reporte generado exitosamente para documento 1152213619" 1
2025-07-17 09:52:57,059 - AGI - INFO - Reporte generado exitosamente para documento: 1152213619
2025-07-17 09:53:03,720 - AGI - INFO - Ejecutando comando: python3.11 main.py 1152213619 --limite 5 --destinatario <EMAIL>
2025-07-17 09:54:41,436 - AGI - INFO - Código de salida: 0
2025-07-17 09:54:41,437 - AGI - INFO - STDOUT: Reporte enviado <NAME_EMAIL>

2025-07-17 09:54:41,437 - AGI - ERROR - STDERR: /opt/anaconda3/lib/python3.11/site-packages/urllib3/connectionpool.py:1063: InsecureRequestWarning: Unverified HTTPS request is being made to host 'mosaico.arus.com.co'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/1.26.x/advanced-usage.html#ssl-warnings
  warnings.warn(

2025-07-17 09:54:41,437 - AGI - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_RESULT "SUCCESS"
2025-07-17 09:54:41,437 - AGI - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_MESSAGE "Reporte generado exitosamente para 1152213619"
2025-07-17 09:54:41,437 - AGI - INFO - AGI Response: VERBOSE "Reporte generado exitosamente para documento 1152213619" 1
2025-07-17 09:54:41,437 - AGI - INFO - Reporte generado exitosamente para documento: 1152213619
2025-07-17 09:57:15,671 - AGI - INFO - Ejecutando comando: python3.11 main.py 1152213619 --limite 1 --destinatario <EMAIL>
2025-07-17 09:57:37,725 - AGI - INFO - Ejecutando comando: python3.11 main.py 1152213619 --limite 5 --destinatario <EMAIL>
2025-07-17 09:59:14,735 - AGI - INFO - Código de salida: 0
2025-07-17 09:59:14,735 - AGI - INFO - STDOUT: Reporte enviado <NAME_EMAIL>

2025-07-17 09:59:14,736 - AGI - ERROR - STDERR: /opt/anaconda3/lib/python3.11/site-packages/urllib3/connectionpool.py:1063: InsecureRequestWarning: Unverified HTTPS request is being made to host 'mosaico.arus.com.co'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/1.26.x/advanced-usage.html#ssl-warnings
  warnings.warn(

2025-07-17 09:59:14,736 - AGI - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_RESULT "SUCCESS"
2025-07-17 09:59:14,736 - AGI - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_MESSAGE "Reporte generado exitosamente para 1152213619"
2025-07-17 09:59:14,736 - AGI - INFO - AGI Response: VERBOSE "Reporte generado exitosamente para documento 1152213619" 1
2025-07-17 09:59:14,736 - AGI - INFO - Reporte generado exitosamente para documento: 1152213619
2025-07-17 10:01:22,267 - AGI - INFO - Ejecutando comando: python3.11 main.py 1152213619 --limite 3 --destinatario <EMAIL>
2025-07-17 10:02:56,196 - AGI - INFO - Código de salida: 0
2025-07-17 10:02:56,196 - AGI - INFO - STDOUT: Reporte enviado <NAME_EMAIL>

2025-07-17 10:02:56,196 - AGI - ERROR - STDERR: /opt/anaconda3/lib/python3.11/site-packages/urllib3/connectionpool.py:1063: InsecureRequestWarning: Unverified HTTPS request is being made to host 'mosaico.arus.com.co'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/1.26.x/advanced-usage.html#ssl-warnings
  warnings.warn(

2025-07-17 10:02:56,196 - AGI - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_RESULT "SUCCESS"
2025-07-17 10:02:56,196 - AGI - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_MESSAGE "Reporte generado exitosamente para 1152213619"
2025-07-17 10:02:56,196 - AGI - INFO - AGI Response: VERBOSE "Reporte generado exitosamente para documento 1152213619" 1
2025-07-17 10:02:56,196 - AGI - INFO - Reporte generado exitosamente para documento: 1152213619
2025-07-17 10:41:07,462 - AGI - INFO - Ejecutando comando: python3.11 main.py 1152213619 --limite 3 --destinatario <EMAIL>
2025-07-17 10:42:45,375 - AGI - INFO - Código de salida: 0
2025-07-17 10:42:45,376 - AGI - INFO - STDOUT: Reporte enviado <NAME_EMAIL>

2025-07-17 10:42:45,376 - AGI - ERROR - STDERR: /opt/anaconda3/lib/python3.11/site-packages/urllib3/connectionpool.py:1063: InsecureRequestWarning: Unverified HTTPS request is being made to host 'mosaico.arus.com.co'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/1.26.x/advanced-usage.html#ssl-warnings
  warnings.warn(

2025-07-17 10:42:45,376 - AGI - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_RESULT "SUCCESS"
2025-07-17 10:42:45,376 - AGI - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_MESSAGE "Reporte generado exitosamente para 1152213619"
2025-07-17 10:42:45,376 - AGI - INFO - AGI Response: VERBOSE "Reporte generado exitosamente para documento 1152213619" 1
2025-07-17 10:42:45,376 - AGI - INFO - Reporte generado exitosamente para documento: 1152213619
2025-07-17 10:43:33,646 - AGI - INFO - Ejecutando comando: python3.11 main.py 1152213619 --limite 2 --destinatario <EMAIL>
2025-07-17 10:45:07,755 - AGI - INFO - Código de salida: 0
2025-07-17 10:45:07,756 - AGI - INFO - STDOUT: Reporte enviado <NAME_EMAIL>

2025-07-17 10:45:07,756 - AGI - ERROR - STDERR: /opt/anaconda3/lib/python3.11/site-packages/urllib3/connectionpool.py:1063: InsecureRequestWarning: Unverified HTTPS request is being made to host 'mosaico.arus.com.co'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/1.26.x/advanced-usage.html#ssl-warnings
  warnings.warn(

2025-07-17 10:45:07,756 - AGI - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_RESULT "SUCCESS"
2025-07-17 10:45:07,756 - AGI - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_MESSAGE "Reporte generado exitosamente para 1152213619"
2025-07-17 10:45:07,756 - AGI - INFO - AGI Response: VERBOSE "Reporte generado exitosamente para documento 1152213619" 1
2025-07-17 10:45:07,756 - AGI - INFO - Reporte generado exitosamente para documento: 1152213619
2025-07-17 10:49:31,539 - AGI - INFO - Ejecutando comando: python3.11 main.py 1152213619 --limite 2 --destinatario <EMAIL>
2025-07-17 15:08:16,741 - AGI - INFO - Ejecutando comando: python3.11 main.py 1152213619 --limite 3 --destinatario <EMAIL>
2025-07-17 15:08:16,741 - AGI - ERROR - Error en create_ticket_report: [Errno 2] No such file or directory: 'python3.11'
2025-07-17 15:08:16,742 - AGI - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_RESULT "ERROR"
2025-07-17 15:08:16,742 - AGI - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_MESSAGE "Error en create_ticket_report: [Errno 2] No such file or directory: 'python3.11'"
2025-07-17 15:08:16,742 - AGI - INFO - AGI Response: VERBOSE "Error: Error en create_ticket_report: [Errno 2] No such file or directory: 'python3.11'" 1
2025-07-17 15:08:54,224 - AGI - INFO - Ejecutando comando: python3.11 main.py 1152213619 --limite 3 --destinatario <EMAIL>
2025-07-17 15:08:54,224 - AGI - ERROR - Error en create_ticket_report: [Errno 2] No such file or directory: 'python3.11'
2025-07-17 15:08:54,224 - AGI - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_RESULT "ERROR"
2025-07-17 15:08:54,224 - AGI - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_MESSAGE "Error en create_ticket_report: [Errno 2] No such file or directory: 'python3.11'"
2025-07-17 15:08:54,224 - AGI - INFO - AGI Response: VERBOSE "Error: Error en create_ticket_report: [Errno 2] No such file or directory: 'python3.11'" 1
2025-07-17 15:09:43,197 - AGI - INFO - Ejecutando comando: python3 main.py 1152213619 --limite 3 --destinatario <EMAIL>
2025-07-17 15:10:19,326 - AGI - INFO - Código de salida: 0
2025-07-17 15:10:19,326 - AGI - INFO - STDOUT: Reporte enviado <NAME_EMAIL>

2025-07-17 15:10:19,326 - AGI - ERROR - STDERR: /home/<USER>/.local/lib/python3.10/site-packages/urllib3/connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'mosaico.arus.com.co'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(

2025-07-17 15:10:19,326 - AGI - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_RESULT "SUCCESS"
2025-07-17 15:10:19,326 - AGI - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_MESSAGE "Reporte generado exitosamente para 1152213619"
2025-07-17 15:10:19,326 - AGI - INFO - AGI Response: VERBOSE "Reporte generado exitosamente para documento 1152213619" 1
2025-07-17 15:10:19,327 - AGI - INFO - Reporte generado exitosamente para documento: 1152213619
2025-07-17 15:10:54,429 - AGI - INFO - Ejecutando comando: python3 main.py 1152213619 --limite 3 --destinatario <EMAIL>
2025-07-17 15:11:33,910 - AGI - INFO - Código de salida: 0
2025-07-17 15:11:33,910 - AGI - INFO - STDOUT: Reporte enviado <NAME_EMAIL>

2025-07-17 15:11:33,910 - AGI - ERROR - STDERR: /home/<USER>/.local/lib/python3.10/site-packages/urllib3/connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'mosaico.arus.com.co'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(

2025-07-17 15:11:33,910 - AGI - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_RESULT "SUCCESS"
2025-07-17 15:11:33,910 - AGI - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_MESSAGE "Reporte generado exitosamente para 1152213619"
2025-07-17 15:11:33,910 - AGI - INFO - AGI Response: VERBOSE "Reporte generado exitosamente para documento 1152213619" 1
2025-07-17 15:11:33,910 - AGI - INFO - Reporte generado exitosamente para documento: 1152213619
2025-07-17 16:20:04,908 - AGI - INFO - Ejecutando comando: python3 main.py 1152213619 --limite 3
2025-07-17 16:20:24,654 - AGI - INFO - Código de salida: 0
2025-07-17 16:20:24,654 - AGI - INFO - STDOUT: Reporte enviado <NAME_EMAIL>

2025-07-17 16:20:24,654 - AGI - ERROR - STDERR: /home/<USER>/.local/lib/python3.10/site-packages/urllib3/connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'mosaico.arus.com.co'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(

2025-07-17 16:20:24,654 - AGI - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_RESULT "SUCCESS"
2025-07-17 16:20:24,654 - AGI - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_MESSAGE "Reporte generado exitosamente para 1152213619"
2025-07-17 16:20:24,654 - AGI - INFO - AGI Response: VERBOSE "Reporte generado exitosamente para documento 1152213619" 1
2025-07-17 16:20:24,654 - AGI - INFO - Reporte generado exitosamente para documento: 1152213619
2025-07-17 16:20:43,985 - AGI - INFO - Ejecutando comando: python3 main.py 1152213619 --limite 3
2025-07-17 16:21:03,273 - AGI - INFO - Código de salida: 0
2025-07-17 16:21:03,273 - AGI - INFO - STDOUT: Reporte enviado <NAME_EMAIL>

2025-07-17 16:21:03,273 - AGI - ERROR - STDERR: /home/<USER>/.local/lib/python3.10/site-packages/urllib3/connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'mosaico.arus.com.co'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(

2025-07-17 16:21:03,273 - AGI - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_RESULT "SUCCESS"
2025-07-17 16:21:03,273 - AGI - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_MESSAGE "Reporte generado exitosamente para 1152213619"
2025-07-17 16:21:03,273 - AGI - INFO - AGI Response: VERBOSE "Reporte generado exitosamente para documento 1152213619" 1
2025-07-17 16:21:03,273 - AGI - INFO - Reporte generado exitosamente para documento: 1152213619
