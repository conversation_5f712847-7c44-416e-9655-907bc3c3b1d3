import logging
import requests
import os
from dotenv import load_dotenv

# Cargar variables de entorno
load_dotenv()

logger = logging.getLogger()


class Mail:
    """Provides a method to send mails."""

    def __init__(self, to: str = '') -> None:
        """Provides a method to send mails.

        Parameters
        ----------
        to : str, optional
            Recipients, by default ''.
        """
        self.__mail_url = os.getenv('MAIL_URL', 'https://mosaico.arus.com.co:3000/mailer/correo/notificacion')
        self.__to = to

    def send(self, subject: str, message: str, to: str = '') -> None:
        """Sends a mail.

        Parameters
        ----------
        subject : str
            Subject.
        message : str
            Message.
        to : str, optional
            Recipients, by default ''.
        """
        try:
            recipient = (to if to else self.__to).strip()
            if not recipient:
                logger.warning(
                    'Correo no enviado. Destinatario no establecido.'
                )
                return

            if not subject or not message:
                logger.warning(
                    'Correo no enviado. El asunto y el mensaje son requeridos.'
                )
                return

            data = {'correo': recipient, 'asunto': subject, 'mensaje': message}
            headers = {'Content-Type': 'application/json'}
            timeout = int(os.getenv('EMAIL_TIMEOUT', '10'))
            verify_ssl = os.getenv('EMAIL_VERIFY_SSL', 'false').lower() == 'true'
            response = requests.post(
                self.__mail_url,
                json=data,
                headers=headers,
                verify=verify_ssl,
                timeout=timeout,
            )
            response.raise_for_status()

            response_data = response.json()
            assert response_data.get('state') == 'sent', response.text
            logger.info(f'Correo enviado correctamente a {recipient}')
        except Exception as e:
            logger.error(f'No se pudo enviar el correo: {str(e)}')
 
 
 
