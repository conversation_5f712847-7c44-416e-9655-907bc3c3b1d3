#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de prueba AGI para Asterisk
Simula la interfaz AGI básica para probar la aplicación BMC Remedy
"""

import sys
import os
import subprocess

def agi_response(command):
    """Simula respuesta AGI"""
    print(f"200 result=0 ({command})")
    sys.stdout.flush()

def agi_get_variable(var_name):
    """Simula obtener variable AGI"""
    # Para pruebas, devolvemos valores simulados
    if var_name == "CALLERID(num)":
        return "1152213619"  # Cédula de prueba
    elif var_name == "CHANNEL":
        return "SIP/test-001"
    return ""

def main():
    """Función principal AGI"""
    # Simular lectura de variables AGI
    print("agi_request: test")
    print("agi_channel: SIP/test-001")
    print("agi_language: es")
    print("agi_callerid: 1152213619")
    print("agi_calleridname: Test User")
    print("")
    
    # Obtener cédula del caller ID
    cedula = agi_get_variable("CALLERID(num)")
    
    if not cedula:
        agi_response("VERBOSE \"No se pudo obtener la cédula del caller ID\" 1")
        sys.exit(1)
    
    agi_response(f"VERBOSE \"Consultando BMC Remedy para cédula: {cedula}\" 1")
    
    try:
        # Ejecutar la consulta BMC Remedy
        cmd = [
            "python", "main.py", 
            cedula, 
            "--destinatario", "<EMAIL>",
            "--limite", "3"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            agi_response("VERBOSE \"Consulta BMC Remedy exitosa\" 1")
            agi_response("VERBOSE \"Reporte enviado por correo\" 1")
        else:
            agi_response("VERBOSE \"Error en consulta BMC Remedy\" 1")
            agi_response(f"VERBOSE \"Error: {result.stderr}\" 1")
            
    except subprocess.TimeoutExpired:
        agi_response("VERBOSE \"Timeout en consulta BMC Remedy\" 1")
    except Exception as e:
        agi_response(f"VERBOSE \"Error ejecutando consulta: {str(e)}\" 1")
    
    agi_response("VERBOSE \"Proceso AGI completado\" 1")

if __name__ == "__main__":
    main()
