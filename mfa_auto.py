#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script AGI Auto-detector para Asterisk - BMC Remedy
Detecta automáticamente la versión de Python y usa el script apropiado

Uso desde Asterisk:
same => n,AGI(mfa_auto.py, "create", "${document}", "${ticketDetail}", "${cellphone}", "${STRREPLACE(UNIQUEID,".","")}", "${destinatario}", "${limite}")

Este script detecta automáticamente:
- Versión de Python disponible
- Disponibilidad de dataclasses
- Y ejecuta la versión apropiada (mfa.py o mfa_simple.py)
"""

import sys
import os
import subprocess

def detect_python_capabilities():
    """Detectar capacidades de Python y elegir el script apropiado"""
    
    # Verificar si dataclasses está disponible
    try:
        import dataclasses
        has_dataclasses = True
    except ImportError:
        has_dataclasses = False
    
    # Verificar versión de Python
    python_version = sys.version_info
    
    # Determinar qué script usar
    if python_version >= (3, 7) and has_dataclasses:
        script_name = "mfa.py"
        reason = "Python {}.{} con dataclasses disponible".format(python_version.major, python_version.minor)
    else:
        script_name = "mfa_simple.py"
        reason = "Python {}.{} - usando versión compatible".format(python_version.major, python_version.minor)
    
    return script_name, reason

def log_message(message):
    """Log simple a archivo"""
    try:
        log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        log_file = os.path.join(log_dir, "mfa_auto.log")
        
        import datetime
        timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = "{} - MFA_AUTO - {}\n".format(timestamp, message)
        
        with open(log_file, 'a') as f:
            f.write(log_entry)
    except Exception:
        pass  # Fallar silenciosamente

def main():
    """Función principal - detecta y ejecuta el script apropiado"""
    try:
        # Detectar qué script usar
        script_name, reason = detect_python_capabilities()
        
        log_message("Detectado: {} - {}".format(script_name, reason))
        
        # Construir comando para ejecutar el script apropiado
        script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), script_name)
        
        # Verificar que el script existe
        if not os.path.exists(script_path):
            error_msg = "Script {} no encontrado en {}".format(script_name, script_path)
            log_message("ERROR: {}".format(error_msg))
            print('SET VARIABLE TICKET_SEARCH_RESULT "ERROR"')
            print('SET VARIABLE TICKET_SEARCH_MESSAGE "{}"'.format(error_msg))
            sys.exit(1)
        
        # Preparar argumentos
        args = [sys.executable, script_path] + sys.argv[1:]
        
        log_message("Ejecutando: {}".format(' '.join(args)))
        
        # Ejecutar el script apropiado
        try:
            # Pasar stdin, stdout, stderr directamente
            process = subprocess.Popen(
                args,
                stdin=sys.stdin,
                stdout=sys.stdout,
                stderr=sys.stderr
            )
            
            # Esperar a que termine
            exit_code = process.wait()
            
            log_message("Script {} terminó con código: {}".format(script_name, exit_code))
            sys.exit(exit_code)
            
        except Exception as e:
            error_msg = "Error ejecutando {}: {}".format(script_name, str(e))
            log_message("ERROR: {}".format(error_msg))
            print('SET VARIABLE TICKET_SEARCH_RESULT "ERROR"')
            print('SET VARIABLE TICKET_SEARCH_MESSAGE "{}"'.format(error_msg))
            sys.exit(1)
    
    except Exception as e:
        error_msg = "Error fatal en auto-detector: {}".format(str(e))
        log_message("FATAL: {}".format(error_msg))
        print('SET VARIABLE TICKET_SEARCH_RESULT "FATAL_ERROR"')
        print('SET VARIABLE TICKET_SEARCH_MESSAGE "{}"'.format(error_msg))
        sys.exit(1)

if __name__ == "__main__":
    main()
