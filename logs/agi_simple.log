2025-07-17 10:36:44 - AGI_SIMPLE - INFO - Ejecutándose en modo terminal (no AGI)
2025-07-17 10:36:44 - AGI_SIMPLE - INFO - Comando recibido: create con argumentos: ('1152213619', 'Test ticket detail', '3001234567', '1234567890123', '<EMAIL>', '3')
2025-07-17 10:36:44 - AGI_SIMPLE - INFO - Parámetros: document=1152213619, destinatario=<EMAIL>, limite=3
2025-07-17 10:36:44 - AGI_SIMPLE - INFO - Iniciando búsqueda para documento: 1152213619
2025-07-17 10:36:44 - AGI_SIMPLE - INFO - AGI Response: VERBOSE "Buscando tickets para documento: 1152213619" 1
2025-07-17 10:36:44 - AGI_SIMPLE - INFO - Usando Python: python3.11
2025-07-17 10:36:44 - AGI_SIMPLE - INFO - Ejecutando comando: python3.11 main.py 1152213619 --limite 3 --destinatario <EMAIL>
2025-07-17 10:38:20 - AGI_SIMPLE - INFO - Código de salida: 0
2025-07-17 10:38:20 - AGI_SIMPLE - INFO - STDOUT: Reporte enviado <NAME_EMAIL>

2025-07-17 10:38:20 - AGI_SIMPLE - ERROR - STDERR: /opt/anaconda3/lib/python3.11/site-packages/urllib3/connectionpool.py:1063: InsecureRequestWarning: Unverified HTTPS request is being made to host 'mosaico.arus.com.co'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/1.26.x/advanced-usage.html#ssl-warnings
  warnings.warn(

2025-07-17 10:38:20 - AGI_SIMPLE - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_RESULT "SUCCESS"
2025-07-17 10:38:20 - AGI_SIMPLE - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_MESSAGE "Reporte generado exitosamente para 1152213619"
2025-07-17 10:38:20 - AGI_SIMPLE - INFO - AGI Response: VERBOSE "Reporte generado exitosamente para documento 1152213619" 1
2025-07-17 10:38:20 - AGI_SIMPLE - INFO - Reporte generado exitosamente para documento: 1152213619
2025-07-17 10:49:14 - AGI_SIMPLE - INFO - Ejecutándose en modo terminal (no AGI)
2025-07-17 10:49:14 - AGI_SIMPLE - INFO - Comando recibido: 1152213619 con argumentos: ('--destinatario', '<EMAIL>', '--limite', '3')
2025-07-17 10:49:14 - AGI_SIMPLE - ERROR - Acción no reconocida: 1152213619
2025-07-17 10:49:14 - AGI_SIMPLE - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_RESULT "ERROR"
2025-07-17 10:49:14 - AGI_SIMPLE - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_MESSAGE "Acción no reconocida: 1152213619"
2025-07-17 15:08:16 - AGI_SIMPLE - INFO - Ejecutándose en modo terminal (no AGI)
2025-07-17 15:08:16 - AGI_SIMPLE - INFO - Comando recibido: search con argumentos: ('1152213619', 'Prueba desde script', '3001234567', 'test123456', '<EMAIL>', '3')
2025-07-17 15:08:16 - AGI_SIMPLE - INFO - Búsqueda de tickets - Cédula: 1152213619, Destinatario: <EMAIL>, Límite: 3
2025-07-17 15:08:16 - AGI_SIMPLE - INFO - Iniciando búsqueda para documento: 1152213619
2025-07-17 15:08:16 - AGI_SIMPLE - INFO - AGI Response: VERBOSE "Buscando tickets para documento: 1152213619" 1
2025-07-17 15:08:16 - AGI_SIMPLE - INFO - Usando Python: python3.10
2025-07-17 15:08:16 - AGI_SIMPLE - INFO - Ejecutando comando: python3.10 main.py 1152213619 --limite 3 --destinatario <EMAIL>
2025-07-17 15:08:54 - AGI_SIMPLE - INFO - Código de salida: 0
2025-07-17 15:08:54 - AGI_SIMPLE - INFO - STDOUT: Reporte enviado <NAME_EMAIL>

2025-07-17 15:08:54 - AGI_SIMPLE - ERROR - STDERR: /home/<USER>/.local/lib/python3.10/site-packages/urllib3/connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'mosaico.arus.com.co'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(

2025-07-17 15:08:54 - AGI_SIMPLE - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_RESULT "SUCCESS"
2025-07-17 15:08:54 - AGI_SIMPLE - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_MESSAGE "Reporte generado exitosamente para 1152213619"
2025-07-17 15:08:54 - AGI_SIMPLE - INFO - AGI Response: VERBOSE "Reporte generado exitosamente para documento 1152213619" 1
2025-07-17 15:08:54 - AGI_SIMPLE - INFO - Reporte generado exitosamente para documento: 1152213619
2025-07-17 15:10:19 - AGI_SIMPLE - INFO - Ejecutándose en modo terminal (no AGI)
2025-07-17 15:10:19 - AGI_SIMPLE - INFO - Comando recibido: search con argumentos: ('1152213619', 'Prueba desde script', '3001234567', 'test123456', '<EMAIL>', '3')
2025-07-17 15:10:19 - AGI_SIMPLE - INFO - Búsqueda de tickets - Cédula: 1152213619, Destinatario: <EMAIL>, Límite: 3
2025-07-17 15:10:19 - AGI_SIMPLE - INFO - Iniciando búsqueda para documento: 1152213619
2025-07-17 15:10:19 - AGI_SIMPLE - INFO - AGI Response: VERBOSE "Buscando tickets para documento: 1152213619" 1
2025-07-17 15:10:19 - AGI_SIMPLE - INFO - Usando Python: python3.10
2025-07-17 15:10:19 - AGI_SIMPLE - INFO - Ejecutando comando: python3.10 main.py 1152213619 --limite 3 --destinatario <EMAIL>
2025-07-17 15:10:54 - AGI_SIMPLE - INFO - Código de salida: 0
2025-07-17 15:10:54 - AGI_SIMPLE - INFO - STDOUT: Reporte enviado <NAME_EMAIL>

2025-07-17 15:10:54 - AGI_SIMPLE - ERROR - STDERR: /home/<USER>/.local/lib/python3.10/site-packages/urllib3/connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'mosaico.arus.com.co'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(

2025-07-17 15:10:54 - AGI_SIMPLE - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_RESULT "SUCCESS"
2025-07-17 15:10:54 - AGI_SIMPLE - INFO - AGI Response: SET VARIABLE TICKET_SEARCH_MESSAGE "Reporte generado exitosamente para 1152213619"
2025-07-17 15:10:54 - AGI_SIMPLE - INFO - AGI Response: VERBOSE "Reporte generado exitosamente para documento 1152213619" 1
2025-07-17 15:10:54 - AGI_SIMPLE - INFO - Reporte generado exitosamente para documento: 1152213619
