#!/usr/bin/env python
"""
Script de prueba para el AGI simplificado (compatible con Python 2.7+)
"""

import subprocess
import sys
import os

def test_agi_simple_compat():
    print("=== Prueba del AGI Simplificado (Compatible) ===")
    
    # Comando a ejecutar
    cmd = [
        'python', 'mfa_simple.py', 
        'create', 
        '1152213619',           # document (cédula)
        'Test ticket detail',   # ticketDetail
        '3001234567',          # cellphone
        '1234567890123',       # uniqueId
        '<EMAIL>',  # destinatario
        '3'                    # limite (número de casos)
    ]
    
    print("Ejecutando: {}".format(' '.join(cmd)))
    
    try:
        # Ejecutar sin entrada stdin (modo terminal)
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        stdout, stderr = process.communicate()
        
        # Manejar tanto Python 2 como 3
        if hasattr(stdout, 'decode'):
            stdout = stdout.decode('utf-8')
        if hasattr(stderr, 'decode'):
            stderr = stderr.decode('utf-8')
        
        print("Código de salida: {}".format(process.returncode))
        
        if stdout:
            print("=== STDOUT ===")
            print(stdout)
        
        if stderr:
            print("=== STDERR ===")
            print(stderr)
        
        if process.returncode == 0:
            print("✅ Prueba exitosa")
        else:
            print("❌ Prueba falló")
            
    except Exception as e:
        print("❌ Error ejecutando prueba: {}".format(str(e)))

if __name__ == "__main__":
    test_agi_simple_compat()
