version: '3.8'

services:
  bmc-remedy-py36:
    build:
      context: .
      dockerfile: Dockerfile.py36
    container_name: bmc-remedy-py36-container
    environment:
      - PYTHONUNBUFFERED=1
    volumes:
      # Montar directorio de logs para persistencia
      - ./logs:/app/logs
      # Montar archivo .env si existe
      - ./.env:/app/.env:ro
    networks:
      - bmc-network-py36
    # Comando por defecto - se puede sobrescribir al ejecutar
    command: ["python", "main.py", "--help"]
    
  # Servicio opcional para desarrollo/debugging con Python 3.6
  bmc-remedy-py36-dev:
    build:
      context: .
      dockerfile: Dockerfile.py36
    container_name: bmc-remedy-py36-dev
    environment:
      - PYTHONUNBUFFERED=1
      - DEBUG=true
    volumes:
      - ./logs:/app/logs
      - ./.env:/app/.env:ro
      # Montar código fuente para desarrollo
      - .:/app
    networks:
      - bmc-network-py36
    # Mantener el contenedor corriendo para debugging
    command: ["tail", "-f", "/dev/null"]
    profiles:
      - dev

networks:
  bmc-network-py36:
    driver: bridge
