#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Configuration management for BMC Remedy Container application.
Centralizes all configuration settings and provides environment-based configuration.
"""

import os
try:
    from dataclasses import dataclass, field
except ImportError:
    # Fallback para Python < 3.7
    def dataclass(cls):
        return cls
    def field(**kwargs):
        return None
from pathlib import Path
from typing import Optional, Dict, Any
from dotenv import load_dotenv


class LoggingConfig:
    """Configuration for logging system."""
    def __init__(self, level="CRITICAL", output_mode="silent", log_dir="logs",
                 enable_colors=True, max_file_size=10*1024*1024, backup_count=5):
        self.level = level
        self.output_mode = output_mode  # console, file, both, silent
        self.log_dir = log_dir
        self.enable_colors = enable_colors
        self.max_file_size = max_file_size  # 10MB
        self.backup_count = backup_count


class BMCConfig:
    """Configuration for BMC Remedy API."""
    def __init__(self, api_base_url="", login_url="", username="", password="",
                 timeout=30, verify_ssl=False):
        self.api_base_url = api_base_url
        self.login_url = login_url
        self.username = username
        self.password = password
        self.timeout = timeout
        self.verify_ssl = verify_ssl


class EmailConfig:
    """Configuration for email system."""
    def __init__(self, mail_url="", default_recipient="", timeout=10, verify_ssl=False):
        self.mail_url = mail_url
        self.default_recipient = default_recipient
        self.timeout = timeout
        self.verify_ssl = verify_ssl


class ApplicationConfig:
    """Main application configuration."""
    def __init__(self, app_name="bmc-remedy", version="1.0.0", debug=False,
                 silent_mode=False, max_incidents_display=5, logging=None,
                 bmc=None, email=None):
        self.app_name = app_name
        self.version = version
        self.debug = debug
        self.silent_mode = silent_mode
        self.max_incidents_display = max_incidents_display

        # Sub-configurations
        self.logging = logging or LoggingConfig()
        self.bmc = bmc or BMCConfig()
        self.email = email or EmailConfig()


class ConfigManager:
    """Manages application configuration from environment variables and files."""
    
    def __init__(self, env_file: Optional[str] = None):
        """
        Initialize configuration manager.
        
        Args:
            env_file: Path to .env file (optional)
        """
        self.env_file = env_file or ".env"
        self._config: Optional[ApplicationConfig] = None
        self._load_environment()
    
    def _load_environment(self):
        """Load environment variables from .env file if it exists."""
        env_path = Path(self.env_file)
        if env_path.exists():
            load_dotenv(env_path)
    
    def get_config(self) -> ApplicationConfig:
        """
        Get the application configuration.
        
        Returns:
            ApplicationConfig instance with all settings
        """
        if self._config is None:
            self._config = self._build_config()
        return self._config
    
    def _build_config(self) -> ApplicationConfig:
        """Build configuration from environment variables."""
        # Logging configuration
        logging_config = LoggingConfig(
            level=os.getenv("LOG_LEVEL", "INFO").upper(),
            output_mode=os.getenv("LOG_OUTPUT_MODE", "both").lower(),
            log_dir=os.getenv("LOG_DIR", "logs"),
            enable_colors=os.getenv("LOG_ENABLE_COLORS", "true").lower() == "true",
            max_file_size=int(os.getenv("LOG_MAX_FILE_SIZE", "10485760")),  # 10MB
            backup_count=int(os.getenv("LOG_BACKUP_COUNT", "5"))
        )
        
        # BMC configuration
        bmc_config = BMCConfig(
            api_base_url=os.getenv("API_BASE_URL", "https://surasoporteti-qa-restapi.onbmc.com/api"),
            login_url=os.getenv("LOGIN_URL", ""),
            username=os.getenv("USERNAME_", "Integracion.VoiceBot"),
            password=os.getenv("PASSWORD", "$ur@2025*"),
            timeout=int(os.getenv("BMC_TIMEOUT", "30")),
            verify_ssl=os.getenv("BMC_VERIFY_SSL", "false").lower() == "true"
        )
        
        # Email configuration
        email_config = EmailConfig(
            mail_url=os.getenv("MAIL_URL", "https://mosaico.arus.com.co:3000/mailer/correo/notificacion"),
            default_recipient=os.getenv("EMAIL_TO", "<EMAIL>"),
            timeout=int(os.getenv("EMAIL_TIMEOUT", "10")),
            verify_ssl=os.getenv("EMAIL_VERIFY_SSL", "false").lower() == "true"
        )
        
        # Main application configuration
        config = ApplicationConfig(
            app_name=os.getenv("APP_NAME", "bmc-remedy"),
            version=os.getenv("APP_VERSION", "1.0.0"),
            debug=os.getenv("DEBUG", "false").lower() == "true",
            silent_mode=os.getenv("SILENT_MODE", "false").lower() == "true",
            max_incidents_display=int(os.getenv("MAX_INCIDENTS_DISPLAY", "5")),
            logging=logging_config,
            bmc=bmc_config,
            email=email_config
        )
        
        return config
    
    def update_config(self, **kwargs):
        """
        Update configuration values.
        
        Args:
            **kwargs: Configuration values to update
        """
        if self._config is None:
            self._config = self._build_config()
        
        for key, value in kwargs.items():
            if hasattr(self._config, key):
                setattr(self._config, key, value)
    
    def get_env_var(self, key: str, default: Any = None) -> Any:
        """
        Get environment variable value.
        
        Args:
            key: Environment variable name
            default: Default value if not found
            
        Returns:
            Environment variable value or default
        """
        return os.getenv(key, default)
    
    def set_env_var(self, key: str, value: str):
        """
        Set environment variable.
        
        Args:
            key: Environment variable name
            value: Value to set
        """
        os.environ[key] = value
    
    def is_debug_mode(self) -> bool:
        """Check if debug mode is enabled."""
        return self.get_config().debug
    
    def is_silent_mode(self) -> bool:
        """Check if silent mode is enabled."""
        return self.get_config().silent_mode
    
    def get_log_level(self) -> str:
        """Get configured log level."""
        return self.get_config().logging.level
    
    def get_output_mode(self) -> str:
        """Get configured output mode."""
        if self.is_silent_mode():
            return "silent"
        return self.get_config().logging.output_mode


# Global configuration manager instance
_config_manager: Optional[ConfigManager] = None


def get_config_manager(env_file: Optional[str] = None) -> ConfigManager:
    """
    Get the global configuration manager instance.
    
    Args:
        env_file: Path to .env file (optional)
        
    Returns:
        ConfigManager instance
    """
    global _config_manager
    
    if _config_manager is None:
        _config_manager = ConfigManager(env_file)
    
    return _config_manager


def get_config() -> ApplicationConfig:
    """
    Get the application configuration.
    
    Returns:
        ApplicationConfig instance
    """
    return get_config_manager().get_config()


def is_debug_mode() -> bool:
    """Check if debug mode is enabled."""
    return get_config_manager().is_debug_mode()


def is_silent_mode() -> bool:
    """Check if silent mode is enabled."""
    # Si no estamos en debug mode, entonces estamos en silent mode
    return not is_debug_mode()


def is_debug_mode() -> bool:
    """Check if debug mode is enabled."""
    config_manager = get_config_manager()
    return config_manager.get_env_var('DEBUG', 'false').lower() == 'true'


def debug_print(*args, **kwargs):
    """Print only if debug mode is enabled."""
    if is_debug_mode():
        print(*args, **kwargs)


def result_print(*args, **kwargs):
    """Print result messages (only shown in debug mode)."""
    # Solo mostrar si estamos en debug mode
    if is_debug_mode():
        print(*args, **kwargs)


def setup_from_args(args) -> ApplicationConfig:
    """
    Setup configuration from command line arguments.
    
    Args:
        args: Parsed command line arguments
        
    Returns:
        Updated ApplicationConfig instance
    """
    config_manager = get_config_manager()
    
    # Update configuration based on command line arguments
    updates = {}
    
    if hasattr(args, 'debug') and args.debug:
        updates['debug'] = True
        config_manager.set_env_var('DEBUG', 'true')
        config_manager.set_env_var('LOG_LEVEL', 'DEBUG')
        # También cambiar el output mode para mostrar más información
        config_manager.set_env_var('LOG_OUTPUT', 'console')
    
    if hasattr(args, 'silent') and args.silent:
        updates['silent_mode'] = True
        config_manager.set_env_var('SILENT_MODE', 'true')
    
    if hasattr(args, 'verbose') and args.verbose:
        config_manager.set_env_var('LOG_LEVEL', 'DEBUG')
    
    if hasattr(args, 'log_level') and args.log_level:
        config_manager.set_env_var('LOG_LEVEL', args.log_level.upper())
    
    if updates:
        config_manager.update_config(**updates)
    
    return config_manager.get_config()
