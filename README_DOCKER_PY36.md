# BMC Remedy - Docker Python 3.6 para Asterisk AGI

Este documento describe cómo desplegar y usar la aplicación BMC Remedy con Docker y Python 3.6, específicamente diseñado para integrarse con Asterisk AGI.

## 🐳 Despliegue con Docker Python 3.6

### Prerrequisitos
- Docker instalado y funcionando
- Acceso a las APIs de BMC Remedy y servicio de correo

### Construcción de la imagen

```bash
# Construir la imagen con Python 3.6
sudo docker build -f Dockerfile.py36 -t bmc-remedy-py36 .
```

### Verificación de la imagen

```bash
# Verificar que Python 3.6 está instalado
sudo docker run --rm bmc-remedy-py36 python --version
# Salida esperada: Python 3.6.15

# Probar la aplicación
sudo docker run --rm bmc-remedy-py36 python main.py --help
```

## 🚀 Us<PERSON> con Docker

### Comando básico
```bash
sudo docker run --rm \
  -v $(pwd)/logs:/app/logs \
  bmc-remedy-py36 \
  python main.py <cedula> --destinatario <email>
```

### Ejemplos de uso

```bash
# Consulta básica con límite de 3 incidentes
sudo docker run --rm \
  -v $(pwd)/logs:/app/logs \
  bmc-remedy-py36 \
  python main.py 1152213619 --destinatario <EMAIL> --limite 3

# Consulta con todos los incidentes
sudo docker run --rm \
  -v $(pwd)/logs:/app/logs \
  bmc-remedy-py36 \
  python main.py 1152213619 --destinatario <EMAIL> --todos

# Consulta con modo debug
sudo docker run --rm \
  -v $(pwd)/logs:/app/logs \
  bmc-remedy-py36 \
  python main.py 1152213619 --destinatario <EMAIL> --debug
```

## 📞 Integración con Asterisk AGI

### Script AGI para Asterisk

Crear un script AGI en `/var/lib/asterisk/agi-bin/bmc_remedy.py`:

```python
#!/usr/bin/env python3
import sys
import os
import subprocess

def agi_response(command):
    print(f"200 result=0 ({command})")
    sys.stdout.flush()

def main():
    # Leer variables AGI
    agi_vars = {}
    while True:
        line = sys.stdin.readline().strip()
        if not line:
            break
        key, value = line.split(': ', 1)
        agi_vars[key] = value
    
    # Obtener cédula del caller ID
    cedula = agi_vars.get('agi_callerid', '')
    
    if not cedula:
        agi_response("VERBOSE \"No se pudo obtener la cédula\" 1")
        sys.exit(1)
    
    agi_response(f"VERBOSE \"Consultando BMC para cédula: {cedula}\" 1")
    
    try:
        # Ejecutar consulta con Docker
        cmd = [
            "sudo", "docker", "run", "--rm",
            "-v", "/var/log/asterisk/bmc:/app/logs",
            "bmc-remedy-py36",
            "python", "main.py", 
            cedula,
            "--destinatario", "<EMAIL>",
            "--limite", "5"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            agi_response("VERBOSE \"Reporte BMC enviado exitosamente\" 1")
        else:
            agi_response("VERBOSE \"Error en consulta BMC\" 1")
            
    except Exception as e:
        agi_response(f"VERBOSE \"Error: {str(e)}\" 1")

if __name__ == "__main__":
    main()
```

### Configuración en extensions.conf

```ini
[bmc-remedy]
exten => _X.,1,NoOp(Consulta BMC Remedy para ${CALLERID(num)})
same => n,AGI(bmc_remedy.py)
same => n,Hangup()
```

### Permisos necesarios

```bash
# Dar permisos de ejecución al script AGI
chmod +x /var/lib/asterisk/agi-bin/bmc_remedy.py

# Permitir que asterisk ejecute docker (agregar al grupo docker)
sudo usermod -aG docker asterisk

# Crear directorio de logs
sudo mkdir -p /var/log/asterisk/bmc
sudo chown asterisk:asterisk /var/log/asterisk/bmc
```

## 🧪 Pruebas

### Prueba manual
```bash
# Ejecutar el script de prueba
python3 test_agi_docker.py
```

### Prueba desde Asterisk CLI
```bash
# En la consola de Asterisk
asterisk -r
CLI> originate Local/1152213619@bmc-remedy extension 1152213619@bmc-remedy
```

## 📋 Características verificadas

✅ **Python 3.6.15** - Versión compatible con sistemas legacy  
✅ **Conexión a BMC Remedy API** - Autenticación y consultas funcionando  
✅ **Búsqueda por cédula** - Encuentra usuarios e incidentes  
✅ **Generación de reportes** - Formato HTML con datos completos  
✅ **Envío de correos** - Integración con servicio de email  
✅ **Logs detallados** - Para debugging y monitoreo  
✅ **Manejo de errores** - Respuestas apropiadas para AGI  

## 🔧 Configuración

### Variables de entorno (.env)
```bash
# BMC Remedy API
API_BASE_URL=https://surasoporteti-restapi.onbmc.com/api
LOGIN_URL=https://surasoporteti-restapi.onbmc.com/api/jwt/login
USERNAME_=Integracion.VoiceBot
PASSWORD=$ur@2025*

# Email Service
MAIL_URL=https://mosaico.arus.com.co:3000/mailer/correo/notificacion
EMAIL_TO=<EMAIL>

# Application
DEBUG=false
SILENT_MODE=false
MAX_INCIDENTS_DISPLAY=5
```

## 📊 Monitoreo

### Logs disponibles
- `/app/logs/bmc-remedy.log` - Log principal de la aplicación
- `/app/logs/agi.log` - Log específico de operaciones AGI

### Métricas importantes
- Tiempo de respuesta de API BMC Remedy
- Éxito/fallo en envío de correos
- Número de incidentes encontrados por consulta

## 🚨 Solución de problemas

### Error de conexión a BMC Remedy
```bash
# Verificar conectividad
sudo docker run --rm bmc-remedy-py36 curl -k https://surasoporteti-restapi.onbmc.com/api/jwt/login
```

### Error de envío de correo
```bash
# Verificar servicio de correo
sudo docker run --rm bmc-remedy-py36 curl -k https://mosaico.arus.com.co:3000/health
```

### Problemas de permisos Docker
```bash
# Verificar que el usuario puede ejecutar docker
sudo docker run --rm hello-world
```

## 📞 Soporte

Para soporte técnico o reportar problemas:
- Email: <EMAIL>
- Logs: Revisar `/app/logs/` dentro del contenedor
